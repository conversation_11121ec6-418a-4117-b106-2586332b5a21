@echo off
title Essay Evaluation Service - Automated Testing and Deployment
color 0A

echo.
echo ================================================================
echo    Essay Evaluation Service - Automated Setup and Testing
echo ================================================================
echo.

echo Choose an option:
echo.
echo 1. Quick validation and start service
echo 2. Run comprehensive tests
echo 3. Validate environment only  
echo 4. Start service directly
echo 5. Install dependencies only
echo 6. Run unit tests only
echo 7. Full automation (install + test + run)
echo.

set /p choice="Enter your choice (1-7): "
echo.

if "%choice%"=="1" (
    echo [1/2] Running quick validation...
    python validate_env.py
    if %errorlevel%==0 (
        echo.
        echo [2/2] Starting service...
        echo Service will be available at: http://localhost:5000
        echo Press Ctrl+C to stop the service
        echo.
        python app.py
    ) else (
        echo.
        echo ❌ Validation failed. Please fix issues first.
        goto :end
    )
) else if "%choice%"=="2" (
    echo Running comprehensive tests...
    echo This may take a few minutes...
    echo.
    python run_tests.py --comprehensive
    goto :end
) else if "%choice%"=="3" (
    echo Validating environment configuration...
    python validate_env.py
    goto :end
) else if "%choice%"=="4" (
    echo Starting service directly...
    echo Service will be available at: http://localhost:5000
    echo Press Ctrl+C to stop the service
    echo.
    python app.py
) else if "%choice%"=="5" (
    echo Installing dependencies...
    python -m pip install -r requirements.txt
    if %errorlevel%==0 (
        echo.
        echo ✅ Dependencies installed successfully!
    ) else (
        echo.
        echo ❌ Failed to install dependencies.
    )
    goto :end
) else if "%choice%"=="6" (
    echo Running unit tests only...
    python run_tests.py --quick
    goto :end
) else if "%choice%"=="7" (
    echo [1/4] Installing dependencies...
    python -m pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies.
        goto :end
    )
    
    echo.
    echo [2/4] Validating environment...
    python validate_env.py
    if %errorlevel% neq 0 (
        echo ❌ Environment validation failed.
        goto :end
    )
    
    echo.
    echo [3/4] Running tests...
    python run_tests.py --quick
    if %errorlevel% neq 0 (
        echo ❌ Tests failed.
        goto :end
    )
    
    echo.
    echo [4/4] Starting service...
    echo Service will be available at: http://localhost:5000
    echo Press Ctrl+C to stop the service
    echo.
    python app.py
) else (
    echo Invalid choice. Please run the script again.
    goto :end
)

:end
echo.
echo Press any key to exit...
pause >nul
