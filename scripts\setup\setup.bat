@echo off
echo ========================================
echo Essay Evaluation Microservice Setup
echo ========================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo ✅ Python found
python --version

echo.
echo Installing dependencies...
pip install -r requirements.txt

if errorlevel 1 (
    echo ❌ Error: Failed to install dependencies
    echo Try running: pip install --upgrade pip
    echo Then run this setup again
    pause
    exit /b 1
)

echo ✅ Dependencies installed successfully!

REM Clean up any problematic files
echo.
echo Cleaning up import issues...
python cleanup_imports.py

REM Create .env file if it doesn't exist
if not exist .env (
    echo.
    echo Creating .env file template...
    echo OPENROUTER_API_KEY=your_openrouter_api_key_here > .env
    echo OPENROUTER_BASE=https://openrouter.ai/api/v1 >> .env
    echo MODEL_ID=anthropic/claude-3.5-sonnet >> .env
    echo PORT=5000 >> .env
    echo FLASK_DEBUG=true >> .env
    echo.
    echo ✅ Created .env file template
    echo.
    echo ⚠️  IMPORTANT: Edit .env file and add your actual OpenRouter API key
    echo You can get one from: https://openrouter.ai/
    echo.
) else (
    echo ✅ .env file already exists
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Edit .env file and add your OpenRouter API key
echo 2. Run: run.bat
echo 3. Test with: python test_simple.py
echo.
echo The service will be available at: http://localhost:5000
echo.
pause