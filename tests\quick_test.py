#!/usr/bin/env python3
"""
Quick test script to verify the service is working
"""

import requests
import json

def test_service():
    base_url = "http://localhost:5000"

    print("🧪 Testing Essay Evaluation Service")
    print("=" * 40)

    # Test health endpoint
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ Health check: PASSED")
            print(f"   Response: {response.json()}")
        else:
            print(f"❌ Health check: FAILED ({response.status_code})")
            return False
    except Exception as e:
        print(f"❌ Health check: ERROR - {e}")
        return False

    # Test evaluation endpoint
    sample_essay = """
    I am passionate about studying artificial intelligence in Korea because of its advanced technology sector.
    My goal is to pursue a Master's degree at KAIST to research machine learning applications.

    During my undergraduate studies in Computer Science, I maintained a 3.8 GPA and published research papers.
    I have experience in programming, data analysis, and software development.

    As president of the coding club, I organized hackathons and mentored junior students.
    I also volunteered at local schools teaching programming to children.

    After graduation, I plan to return to my home country and establish a tech startup
    that leverages Korean AI innovations to solve local problems.
    """

    try:
        response = requests.post(f"{base_url}/api/v1/evaluation/evaluate",
                               json={"essay": sample_essay},
                               headers={"Content-Type": "application/json"})

        if response.status_code == 200:
            response_data = response.json()
            if response_data.get('success'):
                data = response_data.get('data', {})
                print("✅ Evaluation test: PASSED")
                print(f"   Overall Score: {data.get('overall', 0)}/100")
                print(f"   Word Count: {data.get('word_count', 0)}")
                service_info = data.get('service_info', {})
                print(f"   LLM Available: {service_info.get('llm_available', 'Unknown')}")
                print("   Criteria Scores:")
                criteria = data.get('criteria', {})
                for criterion, details in criteria.items():
                    score = details.get('score', 0) if isinstance(details, dict) else 0
                    print(f"     • {criterion}: {score}/100")
                return True
            else:
                print(f"❌ Evaluation test: API returned error")
                print(f"   Response: {response_data}")
                return False
        else:
            print(f"❌ Evaluation test: FAILED ({response.status_code})")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Evaluation test: ERROR - {e}")
        return False

if __name__ == "__main__":
    success = test_service()
    if success:
        print("\n🎉 All tests passed! Service is working correctly.")
    else:
        print("\n❌ Some tests failed. Check the service status.")
