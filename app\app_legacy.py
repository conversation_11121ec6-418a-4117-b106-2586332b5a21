from flask import Flask, request, jsonify
from flask_cors import CORS
import os
from dotenv import load_dotenv
from evaluation import evaluate

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)  # Enable CORS for frontend integration

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "service": "Essay Evaluation Microservice",
        "version": "2.0.0"
    })

@app.route('/api/v1/evaluate', methods=['POST'])
def evaluate_essay():
    """
    Main evaluation endpoint that accepts an essay and returns scores.

    Expected JSON payload:
    {
        "essay": "The essay text to evaluate..."
    }

    Returns:
    {
        "overall": 85,
        "criteria": {
            "Format Compliance": {"score": 90, "feedback": "..."},
            "Introduction/Hook": {"score": 85, "feedback": "..."},
            "Motivation": {"score": 80, "feedback": "..."},
            "Educational Background": {"score": 88, "feedback": "..."},
            "Extracurricular": {"score": 82, "feedback": "..."}
        },
        "word_count": 756,
        "evaluation_timestamp": "2024-01-15T10:30:00",
        "analysis_details": {...},
        "llm_available": true
    }
    """
    try:
        # Validate request
        if not request.is_json:
            return jsonify({
                "error": "Request must be JSON",
                "code": "INVALID_CONTENT_TYPE"
            }), 400

        data = request.get_json()

        if not data or 'essay' not in data:
            return jsonify({
                "error": "Missing 'essay' field in request body",
                "code": "MISSING_ESSAY_FIELD"
            }), 400

        essay_text = data['essay']

        if not isinstance(essay_text, str):
            return jsonify({
                "error": "Essay must be a string",
                "code": "INVALID_ESSAY_TYPE"
            }), 400

        if not essay_text.strip():
            return jsonify({
                "error": "Essay cannot be empty",
                "code": "EMPTY_ESSAY"
            }), 400

        if len(essay_text) > 10000:  # Reasonable limit
            return jsonify({
                "error": "Essay too long (max 10,000 characters)",
                "code": "ESSAY_TOO_LONG"
            }), 400

        # Evaluate the essay
        result = evaluate(essay_text)

        return jsonify(result), 200

    except ValueError as e:
        return jsonify({
            "error": str(e),
            "code": "VALIDATION_ERROR"
        }), 400

    except Exception as e:
        app.logger.error(f"Evaluation error: {str(e)}")
        return jsonify({
            "error": "Internal server error during evaluation",
            "code": "EVALUATION_ERROR",
            "details": str(e) if app.debug else None
        }), 500

@app.route('/api/v1/criteria', methods=['GET'])
def get_criteria():
    """
    Returns the evaluation criteria and scoring guidelines.
    """
    criteria_info = {
        "criteria": [
            {
                "name": "Format Compliance",
                "description": "Word count, structure, readability, and basic formatting requirements",
                "weight": "20%",
                "components": [
                    "Word count (500-1000 optimal)",
                    "Paragraph structure",
                    "Keyword presence",
                    "Readability metrics",
                    "Personal pronoun usage",
                    "Passive voice check"
                ]
            },
            {
                "name": "Introduction/Hook",
                "description": "Opening's ability to capture attention and set tone",
                "weight": "20%",
                "components": [
                    "Attention-grabbing opening",
                    "Personal connection",
                    "Clear thesis/direction",
                    "Engaging narrative"
                ]
            },
            {
                "name": "Motivation",
                "description": "Clarity of goals, connection to Korea, and future plans",
                "weight": "25%",
                "components": [
                    "Clear academic/career goals",
                    "Specific connection to Korea",
                    "Future plans and impact",
                    "Alignment with program"
                ]
            },
            {
                "name": "Educational Background",
                "description": "Academic achievements and relevant coursework",
                "weight": "20%",
                "components": [
                    "Academic performance",
                    "Relevant coursework",
                    "Research experience",
                    "Intellectual growth"
                ]
            },
            {
                "name": "Extracurricular",
                "description": "Leadership, community involvement, and character development",
                "weight": "15%",
                "components": [
                    "Leadership roles",
                    "Community service",
                    "Awards and recognition",
                    "Character development"
                ]
            }
        ],
        "scoring_scale": {
            "90-100": "Exceptional quality - demonstrates excellence",
            "70-89": "Strong quality - meets high standards",
            "50-69": "Adequate quality - meets basic requirements",
            "30-49": "Below average - needs significant improvement",
            "0-29": "Poor quality - major deficiencies"
        },
        "evaluation_features": {
            "fast_rules": "Instant format and structure analysis",
            "llm_evaluation": "AI-powered content assessment",
            "detailed_feedback": "Specific improvement suggestions",
            "comprehensive_metrics": "Word count, readability, structure analysis"
        }
    }

    return jsonify(criteria_info)

@app.route('/api/v1/analyze', methods=['POST'])
def analyze_essay_structure():
    """
    Analyze essay structure without full evaluation (faster endpoint)
    """
    try:
        if not request.is_json:
            return jsonify({
                "error": "Request must be JSON",
                "code": "INVALID_CONTENT_TYPE"
            }), 400

        data = request.get_json()
        essay_text = data.get('essay', '')

        if not essay_text.strip():
            return jsonify({
                "error": "Essay cannot be empty",
                "code": "EMPTY_ESSAY"
            }), 400

        # Import here to avoid circular imports
        from evaluation import enhanced_fast_rules

        analysis = enhanced_fast_rules(essay_text)

        return jsonify({
            "structure_score": analysis["score"],
            "feedback": analysis["feedback"],
            "details": analysis["details"],
            "analysis_type": "structure_only"
        }), 200

    except Exception as e:
        app.logger.error(f"Analysis error: {str(e)}")
        return jsonify({
            "error": "Internal server error during analysis",
            "code": "ANALYSIS_ERROR"
        }), 500

@app.route('/api/v1/similarity', methods=['POST'])
def analyze_essay_similarity():
    """
    Analyze essay similarity to reference essays using embeddings
    """
    try:
        if not request.is_json:
            return jsonify({
                "error": "Request must be JSON",
                "code": "INVALID_CONTENT_TYPE"
            }), 400

        data = request.get_json()
        essay_text = data.get('essay', '')

        if not essay_text.strip():
            return jsonify({
                "error": "Essay cannot be empty",
                "code": "EMPTY_ESSAY"
            }), 400

        # Import here to avoid circular imports
        try:
            from embedding_similarity import compute_embedding_similarity

            similarity_result = compute_embedding_similarity(essay_text)

            return jsonify({
                "similarity_analysis": similarity_result,
                "analysis_type": "embedding_similarity"
            }), 200

        except ImportError:
            return jsonify({
                "error": "Embedding similarity not available",
                "code": "EMBEDDING_NOT_AVAILABLE",
                "message": "Install required dependencies: numpy, google-generativeai, scikit-learn"
            }), 503

    except Exception as e:
        app.logger.error(f"Similarity analysis error: {str(e)}")
        return jsonify({
            "error": "Internal server error during similarity analysis",
            "code": "SIMILARITY_ANALYSIS_ERROR",
            "details": str(e) if app.debug else None
        }), 500

@app.route('/api/v1/scholarships', methods=['GET'])
def get_scholarships():
    """
    Get all available scholarships
    """
    try:
        from scholarship_criteria import scholarship_evaluator

        scholarships = scholarship_evaluator.get_scholarships()

        return jsonify({
            "scholarships": scholarships,
            "count": len(scholarships)
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting scholarships: {str(e)}")
        return jsonify({
            "error": "Internal server error",
            "code": "SCHOLARSHIPS_ERROR"
        }), 500

@app.route('/api/v1/scholarships/<scholarship_id>/essays', methods=['GET'])
def get_essay_types(scholarship_id):
    """
    Get essay types for a specific scholarship
    """
    try:
        from scholarship_criteria import scholarship_evaluator

        essay_types = scholarship_evaluator.get_essay_types(scholarship_id)

        if not essay_types:
            return jsonify({
                "error": "No essay types found for this scholarship",
                "code": "NO_ESSAY_TYPES"
            }), 404

        return jsonify({
            "scholarship_id": scholarship_id,
            "essay_types": essay_types,
            "count": len(essay_types)
        }), 200

    except Exception as e:
        app.logger.error(f"Error getting essay types: {str(e)}")
        return jsonify({
            "error": "Internal server error",
            "code": "ESSAY_TYPES_ERROR"
        }), 500

@app.route('/api/v1/criteria-check', methods=['POST'])
def criteria_check():
    """
    Analyze essay against scholarship criteria with embedding similarity
    """
    try:
        if not request.is_json:
            return jsonify({
                "error": "Request must be JSON",
                "code": "INVALID_CONTENT_TYPE"
            }), 400

        data = request.get_json()

        # Validate required fields
        text = data.get('text', '').strip()
        scholarship_type = data.get('scholarship_type', '').strip()
        essay_type = data.get('essay_type', '').strip()

        if not text:
            return jsonify({
                "error": "Essay text cannot be empty",
                "code": "EMPTY_TEXT"
            }), 400

        if not scholarship_type:
            return jsonify({
                "error": "Scholarship type is required",
                "code": "MISSING_SCHOLARSHIP_TYPE"
            }), 400

        if not essay_type:
            return jsonify({
                "error": "Essay type is required",
                "code": "MISSING_ESSAY_TYPE"
            }), 400

        # Import and analyze
        from scholarship_criteria import scholarship_evaluator

        result = scholarship_evaluator.analyze_criteria(text, scholarship_type, essay_type)

        # Convert to frontend format
        response = {
            "content": result.content,
            "overall_score": result.overall_score,
            "matches": [
                {
                    "criteria": match.criteria,
                    "match_level": match.match_level.value,
                    "explanation": match.explanation,
                    "score": match.score
                }
                for match in result.matches
            ],
            "embedding_available": result.embedding_available,
            "analysis_details": result.analysis_details
        }

        return jsonify(response), 200

    except ValueError as e:
        return jsonify({
            "error": str(e),
            "code": "VALIDATION_ERROR"
        }), 400

    except Exception as e:
        app.logger.error(f"Criteria check error: {str(e)}")
        return jsonify({
            "error": "Internal server error during criteria analysis",
            "code": "CRITERIA_CHECK_ERROR",
            "details": str(e) if app.debug else None
        }), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        "error": "Endpoint not found",
        "code": "NOT_FOUND",
        "available_endpoints": [
            "GET /health",
            "GET /api/v1/criteria",
            "POST /api/v1/evaluate",
            "POST /api/v1/analyze",
            "POST /api/v1/similarity",
            "GET /api/v1/scholarships",
            "GET /api/v1/scholarships/<scholarship_id>/essays",
            "POST /api/v1/criteria-check"
        ]
    }), 404

@app.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        "error": "Method not allowed",
        "code": "METHOD_NOT_ALLOWED"
    }), 405

if __name__ == '__main__':
    # Check for required environment variables
    required_vars = ['OPENROUTER_API_KEY']
    missing_vars = [var for var in required_vars if not os.getenv(var)]

    if missing_vars:
        print(f"Warning: Missing environment variables: {', '.join(missing_vars)}")
        print("Please set these in your .env file")

    # Run the application
    debug_mode = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    port = int(os.getenv('PORT', 5000))

    print(f"Starting Enhanced Essay Evaluation Microservice on port {port}")
    print(f"Debug mode: {debug_mode}")

    app.run(host='0.0.0.0', port=port, debug=debug_mode)