"""
Essay structure and content analysis routes
"""

from flask import Blueprint, request, jsonify, current_app
from services.analysis_service import AnalysisService
from utils.validators import validate_essay_input
from utils.response_formatter import format_success_response, format_error_response

analysis_bp = Blueprint('analysis', __name__)

@analysis_bp.route('/structure', methods=['POST'])
def analyze_structure():
    """
    Analyze essay structure without full evaluation (faster endpoint)
    """
    try:
        if not request.is_json:
            return format_error_response(
                error="Request must be JSON",
                code="INVALID_CONTENT_TYPE"
            ), 400
        
        data = request.get_json()
        
        # Validate input
        validation_result = validate_essay_input(data)
        if not validation_result['valid']:
            return format_error_response(
                error=validation_result['error'],
                code="VALIDATION_ERROR"
            ), 400
        
        essay_text = data.get('essay')
        
        # Perform structure analysis
        analysis_service = AnalysisService()
        result = analysis_service.analyze_structure(essay_text)
        
        return format_success_response(
            data={
                "structure_score": result["score"],
                "feedback": result["feedback"],
                "details": result["details"],
                "analysis_type": "structure_only"
            },
            message="Structure analysis completed successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Structure analysis error: {str(e)}")
        return format_error_response(
            error="Internal server error during structure analysis",
            code="STRUCTURE_ANALYSIS_ERROR"
        ), 500

@analysis_bp.route('/readability', methods=['POST'])
def analyze_readability():
    """
    Analyze essay readability and language quality
    """
    try:
        if not request.is_json:
            return format_error_response(
                error="Request must be JSON",
                code="INVALID_CONTENT_TYPE"
            ), 400
        
        data = request.get_json()
        
        # Validate input
        validation_result = validate_essay_input(data)
        if not validation_result['valid']:
            return format_error_response(
                error=validation_result['error'],
                code="VALIDATION_ERROR"
            ), 400
        
        essay_text = data.get('essay')
        
        # Perform readability analysis
        analysis_service = AnalysisService()
        result = analysis_service.analyze_readability(essay_text)
        
        return format_success_response(
            data=result,
            message="Readability analysis completed successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Readability analysis error: {str(e)}")
        return format_error_response(
            error="Internal server error during readability analysis",
            code="READABILITY_ANALYSIS_ERROR"
        ), 500

@analysis_bp.route('/keywords', methods=['POST'])
def analyze_keywords():
    """
    Extract and analyze keywords from essay
    """
    try:
        if not request.is_json:
            return format_error_response(
                error="Request must be JSON",
                code="INVALID_CONTENT_TYPE"
            ), 400
        
        data = request.get_json()
        
        # Validate input
        validation_result = validate_essay_input(data)
        if not validation_result['valid']:
            return format_error_response(
                error=validation_result['error'],
                code="VALIDATION_ERROR"
            ), 400
        
        essay_text = data.get('essay')
        scholarship_type = data.get('scholarship_type')
        
        # Perform keyword analysis
        analysis_service = AnalysisService()
        result = analysis_service.analyze_keywords(essay_text, scholarship_type)
        
        return format_success_response(
            data=result,
            message="Keyword analysis completed successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Keyword analysis error: {str(e)}")
        return format_error_response(
            error="Internal server error during keyword analysis",
            code="KEYWORD_ANALYSIS_ERROR"
        ), 500

@analysis_bp.route('/sentiment', methods=['POST'])
def analyze_sentiment():
    """
    Analyze essay sentiment and tone
    """
    try:
        if not request.is_json:
            return format_error_response(
                error="Request must be JSON",
                code="INVALID_CONTENT_TYPE"
            ), 400
        
        data = request.get_json()
        
        # Validate input
        validation_result = validate_essay_input(data)
        if not validation_result['valid']:
            return format_error_response(
                error=validation_result['error'],
                code="VALIDATION_ERROR"
            ), 400
        
        essay_text = data.get('essay')
        
        # Perform sentiment analysis
        analysis_service = AnalysisService()
        result = analysis_service.analyze_sentiment(essay_text)
        
        return format_success_response(
            data=result,
            message="Sentiment analysis completed successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Sentiment analysis error: {str(e)}")
        return format_error_response(
            error="Internal server error during sentiment analysis",
            code="SENTIMENT_ANALYSIS_ERROR"
        ), 500

@analysis_bp.route('/comprehensive', methods=['POST'])
def comprehensive_analysis():
    """
    Perform comprehensive analysis including structure, readability, keywords, and sentiment
    """
    try:
        if not request.is_json:
            return format_error_response(
                error="Request must be JSON",
                code="INVALID_CONTENT_TYPE"
            ), 400
        
        data = request.get_json()
        
        # Validate input
        validation_result = validate_essay_input(data)
        if not validation_result['valid']:
            return format_error_response(
                error=validation_result['error'],
                code="VALIDATION_ERROR"
            ), 400
        
        essay_text = data.get('essay')
        scholarship_type = data.get('scholarship_type')
        
        # Perform comprehensive analysis
        analysis_service = AnalysisService()
        result = analysis_service.comprehensive_analysis(essay_text, scholarship_type)
        
        return format_success_response(
            data=result,
            message="Comprehensive analysis completed successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Comprehensive analysis error: {str(e)}")
        return format_error_response(
            error="Internal server error during comprehensive analysis",
            code="COMPREHENSIVE_ANALYSIS_ERROR"
        ), 500
