# Add embedding function to utils.py
import numpy as np
import requests
import os

def embed(text: str) -> np.ndarray:
    """Get embeddings from OpenRouter API using text-embedding-3-large"""
    api_key = os.getenv('OPENROUTER_API_KEY')
    base_url = os.getenv('OPENROUTER_BASE', 'https://openrouter.ai/api/v1')
    
    if not api_key:
        raise ValueError("OPENROUTER_API_KEY not found in environment variables")
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'HTTP-Referer': 'http://localhost:5000',
        'X-Title': 'Essay Evaluation Service'
    }
    
    payload = {
        'model': 'openai/text-embedding-3-large',
        'input': text
    }
    
    response = requests.post(
        f'{base_url}/embeddings',
        headers=headers,
        json=payload,
        timeout=30
    )
    response.raise_for_status()
    
    result = response.json()
    embedding = np.array(result['data'][0]['embedding'])
    return embedding