import requests
import json

def test_health():
    """Test health endpoint"""
    try:
        response = requests.get('http://localhost:5000/health')
        if response.status_code == 200:
            print("✅ Health check passed")
            print(f"Response: {response.json()}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health check error: {e}")

def test_evaluation():
    """Test evaluation endpoint"""
    sample_essay = """
    The first time I witnessed a child's face light up when they finally grasped a mathematical concept I had been teaching, I knew that education was not just my career choice—it was my calling. This moment, in a small tutoring center in rural Vietnam, crystallized my understanding that learning transcends language barriers and cultural differences.

    My goal is to pursue a Master's in Artificial Intelligence at KAIST, specifically focusing on natural language processing for Southeast Asian languages. Korea's leadership in AI research, combined with companies like Samsung and LG pioneering human-computer interaction, makes it the ideal environment for my research. Upon completion, I plan to return to Thailand to establish an AI research lab that bridges Korean technological innovation with Southeast Asian linguistic diversity.

    During my undergraduate studies in Electrical Engineering at the University of Indonesia, I maintained a GPA of 3.85/4.0 while conducting research on renewable energy systems. My thesis on 'Solar Panel Efficiency Optimization Using Machine Learning' was published in the IEEE Indonesian Conference and earned the Best Undergraduate Research Award.

    As president of the University Environmental Club, I led a team of 50 students in organizing the 'Green Campus Initiative,' which reduced university waste by 40% and installed solar panels across three dormitories.
    """
    
    try:
        response = requests.post(
            'http://localhost:5000/api/v1/evaluate',
            headers={'Content-Type': 'application/json'},
            json={'essay': sample_essay}
        )
        
        if response.status_code == 200:
            print("✅ Evaluation test passed")
            result = response.json()
            print(f"Overall Score: {result['overall']}")
            print("Criteria Scores:")
            for criterion, data in result['criteria'].items():
                print(f"  {criterion}: {data['score']}/100")
        else:
            print(f"❌ Evaluation test failed: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Evaluation test error: {e}")

if __name__ == '__main__':
    print("Testing Essay Evaluation Service...")
    print("Make sure the service is running on http://localhost:5000")
    print()
    
    test_health()
    print()
    test_evaluation()
