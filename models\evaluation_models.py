"""
Data models for evaluation system
"""

from dataclasses import dataclass
from typing import Dict, List, Any, Optional
from enum import Enum
import datetime

class MatchLevel(Enum):
    """Match level for criteria evaluation"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class EvaluationType(Enum):
    """Type of evaluation performed"""
    COMPREHENSIVE = "comprehensive"
    QUICK = "quick"
    STRUCTURE_ONLY = "structure_only"
    SIMILARITY_ONLY = "similarity_only"

@dataclass
class EvaluationCriterion:
    """Individual evaluation criterion"""
    name: str
    weight: float
    description: str
    score_range: tuple = (0, 100)

@dataclass
class CriteriaMatch:
    """Result of criteria matching"""
    criteria: str
    match_level: MatchLevel
    explanation: str
    score: int
    confidence: Optional[float] = None

@dataclass
class EvaluationResult:
    """Complete evaluation result"""
    overall_score: int
    criteria_scores: Dict[str, Dict[str, Any]]
    word_count: int
    evaluation_type: EvaluationType
    timestamp: datetime.datetime
    analysis_details: Dict[str, Any]
    service_info: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "overall": self.overall_score,
            "criteria": self.criteria_scores,
            "word_count": self.word_count,
            "evaluation_type": self.evaluation_type.value,
            "evaluation_timestamp": self.timestamp.isoformat(),
            "analysis_details": self.analysis_details,
            "service_info": self.service_info
        }

@dataclass
class ScholarshipType:
    """Scholarship type definition"""
    id: str
    name: str
    logo: str
    description: str
    country: Optional[str] = None
    website: Optional[str] = None

@dataclass
class EssayType:
    """Essay type definition"""
    id: str
    name: str
    scholarship_id: str
    criteria: List[str]
    instructions: str
    max_words: Optional[int] = None
    min_words: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "id": self.id,
            "name": self.name,
            "scholarship_id": self.scholarship_id,
            "criteria": self.criteria,
            "instructions": self.instructions,
            "max_words": self.max_words,
            "min_words": self.min_words,
            "criteria_count": len(self.criteria)
        }

@dataclass
class CriteriaCheckResult:
    """Result of scholarship criteria checking"""
    content: str
    overall_score: int
    matches: List[CriteriaMatch]
    embedding_available: bool = False
    analysis_details: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "content": self.content,
            "overall_score": self.overall_score,
            "matches": [
                {
                    "criteria": match.criteria,
                    "match_level": match.match_level.value,
                    "explanation": match.explanation,
                    "score": match.score,
                    "confidence": match.confidence
                }
                for match in self.matches
            ],
            "embedding_available": self.embedding_available,
            "analysis_details": self.analysis_details or {}
        }

@dataclass
class SimilarityResult:
    """Result of similarity analysis"""
    overall_similarity_score: float
    criterion_scores: Dict[str, Dict[str, Any]]
    embedding_available: bool
    reference_count: int
    analysis_details: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "overall_similarity_score": self.overall_similarity_score,
            "criterion_scores": self.criterion_scores,
            "embedding_available": self.embedding_available,
            "reference_count": self.reference_count,
            "analysis_details": self.analysis_details or {}
        }

@dataclass
class AnalysisResult:
    """Result of text analysis"""
    analysis_type: str
    score: Optional[float]
    metrics: Dict[str, Any]
    recommendations: List[str]
    details: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "analysis_type": self.analysis_type,
            "score": self.score,
            "metrics": self.metrics,
            "recommendations": self.recommendations,
            "details": self.details or {}
        }

@dataclass
class ComparisonResult:
    """Result of essay comparison"""
    comparison_results: List[Dict[str, Any]]
    statistics: Dict[str, Any]
    scholarship_id: str
    essay_type_id: str
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "comparison_results": self.comparison_results,
            "statistics": self.statistics,
            "scholarship_id": self.scholarship_id,
            "essay_type_id": self.essay_type_id
        }

@dataclass
class ServiceStatus:
    """Service status information"""
    service_name: str
    version: str
    status: str  # operational, degraded, maintenance
    components: Dict[str, str]
    features: Dict[str, bool]
    timestamp: datetime.datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "service": self.service_name,
            "version": self.version,
            "status": self.status,
            "components": self.components,
            "features": self.features,
            "timestamp": self.timestamp.isoformat()
        }

# Utility functions for model creation
def create_evaluation_result(
    overall_score: int,
    criteria_scores: Dict[str, Dict[str, Any]],
    word_count: int,
    evaluation_type: str = "comprehensive",
    analysis_details: Optional[Dict[str, Any]] = None,
    service_info: Optional[Dict[str, Any]] = None
) -> EvaluationResult:
    """Create an EvaluationResult instance"""
    return EvaluationResult(
        overall_score=overall_score,
        criteria_scores=criteria_scores,
        word_count=word_count,
        evaluation_type=EvaluationType(evaluation_type),
        timestamp=datetime.datetime.now(),
        analysis_details=analysis_details or {},
        service_info=service_info or {}
    )

def create_criteria_match(
    criteria: str,
    score: int,
    explanation: str,
    confidence: Optional[float] = None
) -> CriteriaMatch:
    """Create a CriteriaMatch instance with automatic match level"""
    if score >= 80:
        match_level = MatchLevel.HIGH
    elif score >= 60:
        match_level = MatchLevel.MEDIUM
    else:
        match_level = MatchLevel.LOW
    
    return CriteriaMatch(
        criteria=criteria,
        match_level=match_level,
        explanation=explanation,
        score=score,
        confidence=confidence
    )
