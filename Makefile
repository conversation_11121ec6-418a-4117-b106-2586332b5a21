.PHONY: install test run clean deploy

install:
	pip install -r requirements.txt
	pip install -r requirements-dev.txt

test:
	python -m pytest tests/ -v

test-unit:
	python -m pytest tests/unit/ -v

test-integration:
	python -m pytest tests/integration/ -v

run:
	python app/app.py

run-dev:
	FLASK_ENV=development python app/app.py

clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete

deploy:
	./scripts/deployment/deploy.sh

setup:
	./scripts/setup/setup.sh

validate:
	python scripts/maintenance/validate_env.py
