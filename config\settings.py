"""
Application configuration settings
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Base configuration class"""

    # Flask settings
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'

    # API settings
    API_VERSION = 'v1'
    API_TITLE = 'Essay Evaluation Microservice'
    API_DESCRIPTION = 'Advanced essay evaluation with AI and embedding similarity'

    # CORS settings
    CORS_ORIGINS = [
        'http://localhost:3000',
        'http://127.0.0.1:3000',
        'http://localhost:3001',
        'http://127.0.0.1:3001'
    ]

    # Rate limiting
    RATE_LIMIT_ENABLED = os.getenv('RATE_LIMIT_ENABLED', 'False').lower() == 'true'
    RATE_LIMIT_DEFAULT = os.getenv('RATE_LIMIT_DEFAULT', '100 per hour')

    # File upload settings
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    UPLOAD_FOLDER = os.getenv('UPLOAD_FOLDER', 'uploads')

    # Database settings (for future use)
    DATABASE_URL = os.getenv('DATABASE_URL')

    # External API settings
    OPENROUTER_API_KEY = os.getenv('OPENROUTER_API_KEY')
    OPENROUTER_BASE = os.getenv('OPENROUTER_BASE', 'https://openrouter.ai/api/v1')
    MODEL_ID = os.getenv('MODEL_ID', 'anthropic/claude-3.5-sonnet')

    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')

    # Cache settings
    CACHE_TYPE = os.getenv('CACHE_TYPE', 'simple')
    CACHE_DEFAULT_TIMEOUT = int(os.getenv('CACHE_DEFAULT_TIMEOUT', '300'))

    # Logging settings
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'app.log')

    # Service settings
    SERVICE_NAME = 'Essay Evaluation Microservice'
    SERVICE_VERSION = '2.0.0'

    # Feature flags
    ENABLE_EMBEDDING_SIMILARITY = os.getenv('ENABLE_EMBEDDING_SIMILARITY', 'True').lower() == 'true'
    ENABLE_LLM_EVALUATION = os.getenv('ENABLE_LLM_EVALUATION', 'True').lower() == 'true'
    ENABLE_SCHOLARSHIP_CRITERIA = os.getenv('ENABLE_SCHOLARSHIP_CRITERIA', 'True').lower() == 'true'

    # Data directories
    DATA_DIR = os.getenv('DATA_DIR', 'data')
    CACHE_DIR = os.getenv('CACHE_DIR', 'data')

    def get_api_info(self):
        """Get API information"""
        return {
            'title': self.API_TITLE,
            'description': self.API_DESCRIPTION,
            'version': self.SERVICE_VERSION,
            'api_version': self.API_VERSION
        }

    def get_feature_flags(self):
        """Get current feature flags"""
        return {
            'embedding_similarity': self.ENABLE_EMBEDDING_SIMILARITY,
            'llm_evaluation': self.ENABLE_LLM_EVALUATION,
            'scholarship_criteria': self.ENABLE_SCHOLARSHIP_CRITERIA
        }

    @classmethod
    def is_production(cls):
        """Check if running in production"""
        return os.getenv('FLASK_ENV') == 'production'

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False

class TestingConfig(Config):
    """Testing configuration"""
    DEBUG = True
    TESTING = True
    WTF_CSRF_ENABLED = False

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False

    # Enhanced security for production
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'

# Configuration mapping
config_map = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

def get_config():
    """Get configuration based on environment"""
    env = os.getenv('FLASK_ENV', 'development')
    return config_map.get(env, DevelopmentConfig)
