"""
Scholarship-specific evaluation routes
"""

from flask import Blueprint, request, jsonify, current_app
from services.scholarship_service import ScholarshipService
from utils.validators import validate_criteria_check_input
from utils.response_formatter import format_success_response, format_error_response

scholarship_bp = Blueprint('scholarship', __name__)

@scholarship_bp.route('/list', methods=['GET'])
def get_scholarships():
    """
    Get all available scholarships
    """
    try:
        scholarship_service = ScholarshipService()
        scholarships = scholarship_service.get_scholarships()
        
        return format_success_response(
            data={
                "scholarships": scholarships,
                "count": len(scholarships)
            },
            message="Scholarships retrieved successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Error getting scholarships: {str(e)}")
        return format_error_response(
            error="Failed to retrieve scholarships",
            code="SCHOLARSHIPS_ERROR"
        ), 500

@scholarship_bp.route('/<scholarship_id>/essays', methods=['GET'])
def get_essay_types(scholarship_id):
    """
    Get essay types for a specific scholarship
    """
    try:
        scholarship_service = ScholarshipService()
        essay_types = scholarship_service.get_essay_types(scholarship_id)
        
        if not essay_types:
            return format_error_response(
                error="No essay types found for this scholarship",
                code="NO_ESSAY_TYPES"
            ), 404
        
        return format_success_response(
            data={
                "scholarship_id": scholarship_id,
                "essay_types": essay_types,
                "count": len(essay_types)
            },
            message="Essay types retrieved successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Error getting essay types: {str(e)}")
        return format_error_response(
            error="Failed to retrieve essay types",
            code="ESSAY_TYPES_ERROR"
        ), 500

@scholarship_bp.route('/<scholarship_id>/essays/<essay_type_id>', methods=['GET'])
def get_essay_details(scholarship_id, essay_type_id):
    """
    Get detailed information about a specific essay type
    """
    try:
        scholarship_service = ScholarshipService()
        essay_details = scholarship_service.get_essay_type_details(scholarship_id, essay_type_id)
        
        if not essay_details:
            return format_error_response(
                error="Essay type not found",
                code="ESSAY_TYPE_NOT_FOUND"
            ), 404
        
        return format_success_response(
            data=essay_details,
            message="Essay details retrieved successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Error getting essay details: {str(e)}")
        return format_error_response(
            error="Failed to retrieve essay details",
            code="ESSAY_DETAILS_ERROR"
        ), 500

@scholarship_bp.route('/criteria-check', methods=['POST'])
def criteria_check():
    """
    Analyze essay against scholarship criteria with embedding similarity
    """
    try:
        if not request.is_json:
            return format_error_response(
                error="Request must be JSON",
                code="INVALID_CONTENT_TYPE"
            ), 400
        
        data = request.get_json()
        
        # Validate input
        validation_result = validate_criteria_check_input(data)
        if not validation_result['valid']:
            return format_error_response(
                error=validation_result['error'],
                code="VALIDATION_ERROR"
            ), 400
        
        text = data.get('text')
        scholarship_type = data.get('scholarship_type')
        essay_type = data.get('essay_type')
        
        # Perform criteria analysis
        scholarship_service = ScholarshipService()
        result = scholarship_service.analyze_criteria(text, scholarship_type, essay_type)
        
        return format_success_response(
            data=result,
            message="Criteria analysis completed successfully"
        )
        
    except ValueError as e:
        return format_error_response(
            error=str(e),
            code="VALIDATION_ERROR"
        ), 400
        
    except Exception as e:
        current_app.logger.error(f"Criteria check error: {str(e)}")
        return format_error_response(
            error="Internal server error during criteria analysis",
            code="CRITERIA_CHECK_ERROR",
            details=str(e) if current_app.debug else None
        ), 500

@scholarship_bp.route('/compare', methods=['POST'])
def compare_essays():
    """
    Compare multiple essays for the same scholarship criteria
    """
    try:
        if not request.is_json:
            return format_error_response(
                error="Request must be JSON",
                code="INVALID_CONTENT_TYPE"
            ), 400
        
        data = request.get_json()
        
        essays = data.get('essays', [])
        scholarship_type = data.get('scholarship_type')
        essay_type = data.get('essay_type')
        
        if not essays or len(essays) < 2:
            return format_error_response(
                error="At least 2 essays required for comparison",
                code="INSUFFICIENT_ESSAYS"
            ), 400
        
        if len(essays) > 5:
            return format_error_response(
                error="Maximum 5 essays allowed for comparison",
                code="TOO_MANY_ESSAYS"
            ), 400
        
        # Perform comparison
        scholarship_service = ScholarshipService()
        comparison_result = scholarship_service.compare_essays(essays, scholarship_type, essay_type)
        
        return format_success_response(
            data=comparison_result,
            message="Essay comparison completed successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Essay comparison error: {str(e)}")
        return format_error_response(
            error="Internal server error during essay comparison",
            code="COMPARISON_ERROR"
        ), 500
