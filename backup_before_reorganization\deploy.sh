#!/bin/bash

# Essay Evaluation Microservice Deployment Script

set -e

echo "🚀 Starting Essay Evaluation Microservice Deployment"

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create one with your API keys."
    echo "📝 Copy .env.example to .env and fill in your values:"
    echo "   cp .env .env.local"
    echo "   # Edit .env.local with your actual API keys"
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Load environment variables
source .env

# Validate required environment variables
if [ -z "$OPENROUTER_API_KEY" ]; then
    echo "❌ OPENROUTER_API_KEY is not set in .env file"
    exit 1
fi

echo "✅ Environment validation passed"

# Build and start services
echo "🔨 Building Docker images..."
docker-compose build

echo "🚀 Starting services..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Health check
echo "🏥 Performing health check..."
if curl -f http://localhost:5000/health > /dev/null 2>&1; then
    echo "✅ Service is healthy and running!"
    echo "🌐 Service available at: http://localhost:5000"
    echo "📊 Health check: http://localhost:5000/health"
    echo "📈 Metrics: http://localhost:5000/metrics"
    echo "📋 API docs: http://localhost:5000/api/v1/criteria"
else
    echo "❌ Health check failed. Checking logs..."
    docker-compose logs essay-evaluator
    exit 1
fi

echo "🎉 Deployment completed successfully!"
echo ""
echo "📚 Quick Start:"
echo "  Test evaluation: curl -X POST http://localhost:5000/api/v1/evaluate -H 'Content-Type: application/json' -d '{\"essay\":\"Your essay here\"}'"
echo "  View logs: docker-compose logs -f essay-evaluator"
echo "  Stop services: docker-compose down"
echo ""
