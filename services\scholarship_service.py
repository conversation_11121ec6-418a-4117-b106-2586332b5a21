"""
Scholarship-specific evaluation service
Handles scholarship criteria and essay type management
"""

import os
import sys
from typing import Dict, List, Any, Optional

# Add the parent directory to the path to import legacy modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class ScholarshipService:
    """
    Service for scholarship-specific essay evaluation
    """
    
    def __init__(self):
        self._load_scholarship_evaluator()
    
    def _load_scholarship_evaluator(self):
        """Load the scholarship criteria evaluator"""
        try:
            from scholarship_criteria import scholarship_evaluator
            self.evaluator = scholarship_evaluator
            self.available = True
        except ImportError as e:
            print(f"Warning: Scholarship evaluator not available: {e}")
            self.evaluator = None
            self.available = False
    
    def get_scholarships(self) -> List[Dict[str, str]]:
        """Get all available scholarships"""
        if not self.available:
            return self._get_default_scholarships()
        
        try:
            return self.evaluator.get_scholarships()
        except Exception:
            return self._get_default_scholarships()
    
    def get_essay_types(self, scholarship_id: str) -> List[Dict[str, Any]]:
        """Get essay types for a specific scholarship"""
        if not self.available:
            return []
        
        try:
            return self.evaluator.get_essay_types(scholarship_id)
        except Exception:
            return []
    
    def get_essay_type_details(self, scholarship_id: str, essay_type_id: str) -> Optional[Dict[str, Any]]:
        """Get specific essay type details"""
        if not self.available:
            return None
        
        try:
            essay_type = self.evaluator.get_essay_type_details(scholarship_id, essay_type_id)
            if essay_type:
                return {
                    "id": essay_type.id,
                    "name": essay_type.name,
                    "scholarship_id": essay_type.scholarship_id,
                    "criteria": essay_type.criteria,
                    "instructions": essay_type.instructions,
                    "max_words": essay_type.max_words,
                    "criteria_count": len(essay_type.criteria)
                }
            return None
        except Exception:
            return None
    
    def analyze_criteria(self, text: str, scholarship_id: str, essay_type_id: str) -> Dict[str, Any]:
        """
        Analyze essay against scholarship criteria
        """
        if not self.available:
            raise ValueError("Scholarship evaluation service not available")
        
        if not text or not text.strip():
            raise ValueError("Essay text cannot be empty")
        
        if not scholarship_id:
            raise ValueError("Scholarship type is required")
        
        if not essay_type_id:
            raise ValueError("Essay type is required")
        
        try:
            result = self.evaluator.analyze_criteria(text, scholarship_id, essay_type_id)
            
            # Convert to API response format
            return {
                "content": result.content,
                "overall_score": result.overall_score,
                "matches": [
                    {
                        "criteria": match.criteria,
                        "match_level": match.match_level.value,
                        "explanation": match.explanation,
                        "score": match.score
                    }
                    for match in result.matches
                ],
                "embedding_available": result.embedding_available,
                "analysis_details": result.analysis_details
            }
            
        except Exception as e:
            raise ValueError(f"Analysis failed: {str(e)}")
    
    def compare_essays(self, essays: List[str], scholarship_id: str, essay_type_id: str) -> Dict[str, Any]:
        """
        Compare multiple essays for the same scholarship criteria
        """
        if not self.available:
            raise ValueError("Scholarship evaluation service not available")
        
        if not essays or len(essays) < 2:
            raise ValueError("At least 2 essays required for comparison")
        
        results = []
        
        for i, essay in enumerate(essays):
            try:
                analysis = self.analyze_criteria(essay, scholarship_id, essay_type_id)
                results.append({
                    "essay_index": i,
                    "word_count": len(essay.split()),
                    "overall_score": analysis["overall_score"],
                    "criteria_scores": {
                        match["criteria"]: match["score"] 
                        for match in analysis["matches"]
                    },
                    "embedding_available": analysis.get("embedding_available", False)
                })
            except Exception as e:
                results.append({
                    "essay_index": i,
                    "error": str(e),
                    "overall_score": 0
                })
        
        # Calculate comparison statistics
        valid_results = [r for r in results if "error" not in r]
        
        if not valid_results:
            raise ValueError("No essays could be analyzed successfully")
        
        # Find best and worst performing essays
        best_essay = max(valid_results, key=lambda x: x["overall_score"])
        worst_essay = min(valid_results, key=lambda x: x["overall_score"])
        
        # Calculate average scores by criteria
        all_criteria = set()
        for result in valid_results:
            all_criteria.update(result.get("criteria_scores", {}).keys())
        
        criteria_averages = {}
        for criterion in all_criteria:
            scores = [
                result["criteria_scores"].get(criterion, 0) 
                for result in valid_results 
                if criterion in result.get("criteria_scores", {})
            ]
            if scores:
                criteria_averages[criterion] = {
                    "average": round(sum(scores) / len(scores), 1),
                    "min": min(scores),
                    "max": max(scores)
                }
        
        return {
            "comparison_results": results,
            "statistics": {
                "total_essays": len(essays),
                "successfully_analyzed": len(valid_results),
                "average_score": round(sum(r["overall_score"] for r in valid_results) / len(valid_results), 1) if valid_results else 0,
                "best_essay": {
                    "index": best_essay["essay_index"],
                    "score": best_essay["overall_score"]
                },
                "worst_essay": {
                    "index": worst_essay["essay_index"],
                    "score": worst_essay["overall_score"]
                },
                "criteria_averages": criteria_averages
            },
            "scholarship_id": scholarship_id,
            "essay_type_id": essay_type_id
        }
    
    def _get_default_scholarships(self) -> List[Dict[str, str]]:
        """Default scholarships when service is not available"""
        return [
            {
                "id": "gks",
                "name": "Global Korea Scholarship",
                "logo": "🇰🇷",
                "description": "Korean government scholarship for international students"
            },
            {
                "id": "chevening",
                "name": "Chevening Scholarship",
                "logo": "🇬🇧",
                "description": "UK government scholarship program"
            },
            {
                "id": "fulbright",
                "name": "Fulbright Program",
                "logo": "🇺🇸",
                "description": "US government educational exchange program"
            }
        ]
