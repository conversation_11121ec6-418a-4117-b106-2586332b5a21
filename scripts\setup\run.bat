@echo off
echo Starting Essay Evaluation Microservice...

REM Check if .env file exists
if not exist .env (
    echo Error: .env file not found!
    echo Please create a .env file with your OpenRouter API key.
    echo Example:
    echo OPENROUTER_API_KEY=your_api_key_here
    echo OPENROUTER_BASE=https://openrouter.ai/api/v1
    echo MODEL_ID=anthropic/claude-3.5-sonnet
    pause
    exit /b 1
)

REM Set environment variables for this session
set FLASK_DEBUG=true
set PORT=5000

echo Environment variables set
echo Starting Flask application...

python app.py

pause
