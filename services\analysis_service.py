"""
Essay analysis service
Handles structure, readability, keyword, and sentiment analysis
"""

import re
import os
import sys
from typing import Dict, List, Any, Optional
from collections import Counter

# Add the parent directory to the path to import legacy modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class AnalysisService:
    """
    Service for various essay analysis functions
    """
    
    def __init__(self):
        pass
    
    def analyze_structure(self, essay_text: str) -> Dict[str, Any]:
        """
        Analyze essay structure and formatting
        """
        try:
            from evaluation import enhanced_fast_rules
            return enhanced_fast_rules(essay_text)
        except ImportError:
            return self._basic_structure_analysis(essay_text)
    
    def analyze_readability(self, essay_text: str) -> Dict[str, Any]:
        """
        Analyze essay readability and language quality
        """
        words = essay_text.split()
        sentences = re.split(r'[.!?]+', essay_text)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        # Basic readability metrics
        word_count = len(words)
        sentence_count = len(sentences)
        avg_words_per_sentence = word_count / sentence_count if sentence_count > 0 else 0
        
        # Character count
        char_count = len(essay_text)
        avg_chars_per_word = char_count / word_count if word_count > 0 else 0
        
        # Complex words (more than 6 characters)
        complex_words = [word for word in words if len(word) > 6]
        complex_word_ratio = len(complex_words) / word_count if word_count > 0 else 0
        
        # Calculate readability score (simplified Flesch Reading Ease)
        if sentence_count > 0 and word_count > 0:
            flesch_score = 206.835 - (1.015 * avg_words_per_sentence) - (84.6 * (len([c for c in essay_text if c in 'aeiouAEIOU']) / word_count))
            flesch_score = max(0, min(100, flesch_score))
        else:
            flesch_score = 0
        
        # Determine readability level
        if flesch_score >= 80:
            readability_level = "Very Easy"
        elif flesch_score >= 70:
            readability_level = "Easy"
        elif flesch_score >= 60:
            readability_level = "Standard"
        elif flesch_score >= 50:
            readability_level = "Fairly Difficult"
        elif flesch_score >= 30:
            readability_level = "Difficult"
        else:
            readability_level = "Very Difficult"
        
        return {
            "readability_score": round(flesch_score, 1),
            "readability_level": readability_level,
            "metrics": {
                "word_count": word_count,
                "sentence_count": sentence_count,
                "character_count": char_count,
                "avg_words_per_sentence": round(avg_words_per_sentence, 1),
                "avg_chars_per_word": round(avg_chars_per_word, 1),
                "complex_word_ratio": round(complex_word_ratio * 100, 1)
            },
            "recommendations": self._get_readability_recommendations(flesch_score, avg_words_per_sentence)
        }
    
    def analyze_keywords(self, essay_text: str, scholarship_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Extract and analyze keywords from essay
        """
        # Clean and tokenize text
        words = re.findall(r'\b[a-zA-Z]+\b', essay_text.lower())
        
        # Remove common stop words
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
            'above', 'below', 'between', 'among', 'is', 'are', 'was', 'were', 'be', 'been',
            'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
        }
        
        filtered_words = [word for word in words if word not in stop_words and len(word) > 2]
        
        # Count word frequencies
        word_freq = Counter(filtered_words)
        
        # Get most common words
        common_words = word_freq.most_common(20)
        
        # Scholarship-specific keywords
        scholarship_keywords = self._get_scholarship_keywords(scholarship_type)
        
        # Check for scholarship-relevant keywords
        found_keywords = []
        for category, keywords in scholarship_keywords.items():
            found = [word for word in filtered_words if word in keywords]
            if found:
                found_keywords.append({
                    "category": category,
                    "keywords": list(set(found)),
                    "count": len(found)
                })
        
        # Calculate keyword density
        total_words = len(filtered_words)
        keyword_density = {}
        for category_data in found_keywords:
            density = (category_data["count"] / total_words * 100) if total_words > 0 else 0
            keyword_density[category_data["category"]] = round(density, 2)
        
        return {
            "total_unique_words": len(set(filtered_words)),
            "most_common_words": common_words[:10],
            "scholarship_keywords": found_keywords,
            "keyword_density": keyword_density,
            "recommendations": self._get_keyword_recommendations(found_keywords, scholarship_type)
        }
    
    def analyze_sentiment(self, essay_text: str) -> Dict[str, Any]:
        """
        Analyze essay sentiment and tone
        """
        # Simple sentiment analysis using word lists
        positive_words = {
            'excellent', 'outstanding', 'amazing', 'wonderful', 'fantastic', 'great', 'good',
            'positive', 'successful', 'achievement', 'accomplish', 'passionate', 'excited',
            'motivated', 'inspired', 'confident', 'optimistic', 'enthusiastic', 'dedicated',
            'committed', 'determined', 'ambitious', 'innovative', 'creative', 'leadership'
        }
        
        negative_words = {
            'bad', 'terrible', 'awful', 'horrible', 'poor', 'failed', 'failure', 'difficult',
            'challenging', 'struggle', 'problem', 'issue', 'concern', 'worried', 'anxious',
            'disappointed', 'frustrated', 'confused', 'uncertain', 'doubt', 'fear'
        }
        
        words = re.findall(r'\b[a-zA-Z]+\b', essay_text.lower())
        
        positive_count = sum(1 for word in words if word in positive_words)
        negative_count = sum(1 for word in words if word in negative_words)
        total_words = len(words)
        
        # Calculate sentiment score
        if total_words > 0:
            sentiment_score = (positive_count - negative_count) / total_words * 100
        else:
            sentiment_score = 0
        
        # Determine sentiment category
        if sentiment_score > 2:
            sentiment_category = "Very Positive"
        elif sentiment_score > 0.5:
            sentiment_category = "Positive"
        elif sentiment_score > -0.5:
            sentiment_category = "Neutral"
        elif sentiment_score > -2:
            sentiment_category = "Negative"
        else:
            sentiment_category = "Very Negative"
        
        # Analyze tone indicators
        formal_indicators = ['furthermore', 'moreover', 'consequently', 'therefore', 'however', 'nevertheless']
        informal_indicators = ['really', 'pretty', 'quite', 'very', 'super', 'totally']
        
        formal_count = sum(1 for word in words if word in formal_indicators)
        informal_count = sum(1 for word in words if word in informal_indicators)
        
        if formal_count > informal_count:
            tone = "Formal"
        elif informal_count > formal_count:
            tone = "Informal"
        else:
            tone = "Neutral"
        
        return {
            "sentiment_score": round(sentiment_score, 2),
            "sentiment_category": sentiment_category,
            "tone": tone,
            "metrics": {
                "positive_words": positive_count,
                "negative_words": negative_count,
                "formal_indicators": formal_count,
                "informal_indicators": informal_count,
                "total_words": total_words
            },
            "recommendations": self._get_sentiment_recommendations(sentiment_score, tone)
        }
    
    def comprehensive_analysis(self, essay_text: str, scholarship_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Perform comprehensive analysis including all analysis types
        """
        return {
            "structure": self.analyze_structure(essay_text),
            "readability": self.analyze_readability(essay_text),
            "keywords": self.analyze_keywords(essay_text, scholarship_type),
            "sentiment": self.analyze_sentiment(essay_text),
            "summary": self._generate_analysis_summary(essay_text, scholarship_type)
        }
    
    def _basic_structure_analysis(self, essay_text: str) -> Dict[str, Any]:
        """Basic structure analysis when enhanced rules are not available"""
        word_count = len(essay_text.split())
        paragraph_count = len([p for p in essay_text.split('\n\n') if p.strip()])
        
        score = 50  # Base score
        feedback = []
        
        # Word count analysis
        if 200 <= word_count <= 800:
            score += 20
            feedback.append("Word count is appropriate.")
        elif word_count < 200:
            score -= 15
            feedback.append("Essay is too short. Consider adding more details.")
        else:
            score -= 10
            feedback.append("Essay is quite long. Consider being more concise.")
        
        # Paragraph structure
        if paragraph_count >= 3:
            score += 15
            feedback.append("Good paragraph structure.")
        else:
            score -= 10
            feedback.append("Consider organizing content into more paragraphs.")
        
        return {
            "score": max(0, min(100, score)),
            "feedback": " ".join(feedback),
            "details": {
                "word_count": word_count,
                "paragraph_count": paragraph_count
            }
        }
    
    def _get_scholarship_keywords(self, scholarship_type: Optional[str]) -> Dict[str, List[str]]:
        """Get relevant keywords for different scholarship types"""
        base_keywords = {
            "academic": ["education", "study", "research", "university", "degree", "academic", "scholar", "learning"],
            "motivation": ["goal", "objective", "aspiration", "dream", "vision", "purpose", "motivation", "passion"],
            "leadership": ["leadership", "leader", "manage", "organize", "coordinate", "team", "responsibility"],
            "experience": ["experience", "internship", "volunteer", "work", "project", "activity", "involvement"]
        }
        
        if scholarship_type == "gks":
            base_keywords["korea"] = ["korea", "korean", "seoul", "kaist", "snu", "technology", "innovation"]
        elif scholarship_type == "chevening":
            base_keywords["uk"] = ["uk", "britain", "british", "london", "oxford", "cambridge", "networking"]
        elif scholarship_type == "fulbright":
            base_keywords["usa"] = ["usa", "america", "american", "exchange", "cultural", "diversity"]
        
        return base_keywords
    
    def _get_readability_recommendations(self, score: float, avg_sentence_length: float) -> List[str]:
        """Get readability improvement recommendations"""
        recommendations = []
        
        if score < 50:
            recommendations.append("Consider using shorter sentences and simpler words.")
        if avg_sentence_length > 25:
            recommendations.append("Try to break down long sentences for better readability.")
        if score > 80:
            recommendations.append("Good readability! Your essay is easy to understand.")
        
        return recommendations
    
    def _get_keyword_recommendations(self, found_keywords: List[Dict], scholarship_type: Optional[str]) -> List[str]:
        """Get keyword usage recommendations"""
        recommendations = []
        
        categories_found = [kw["category"] for kw in found_keywords]
        
        if "academic" not in categories_found:
            recommendations.append("Consider adding more academic-related keywords.")
        if "motivation" not in categories_found:
            recommendations.append("Include more words about your goals and motivations.")
        if scholarship_type and scholarship_type not in categories_found:
            recommendations.append(f"Add more {scholarship_type}-specific keywords.")
        
        return recommendations
    
    def _get_sentiment_recommendations(self, sentiment_score: float, tone: str) -> List[str]:
        """Get sentiment and tone recommendations"""
        recommendations = []
        
        if sentiment_score < 0:
            recommendations.append("Consider using more positive language to convey enthusiasm.")
        if tone == "Informal":
            recommendations.append("Use more formal language appropriate for academic applications.")
        if sentiment_score > 0 and tone == "Formal":
            recommendations.append("Good balance of positive sentiment and formal tone.")
        
        return recommendations
    
    def _generate_analysis_summary(self, essay_text: str, scholarship_type: Optional[str]) -> Dict[str, Any]:
        """Generate overall analysis summary"""
        word_count = len(essay_text.split())
        
        return {
            "word_count": word_count,
            "scholarship_type": scholarship_type,
            "analysis_complete": True,
            "overall_assessment": "Comprehensive analysis completed successfully."
        }
