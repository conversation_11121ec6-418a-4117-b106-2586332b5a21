import re
import json
import requests
from typing import Dict, Any
import os
from dotenv import load_dotenv
import datetime

# Import embedding similarity (with fallback if not available)
try:
    from embedding_similarity import compute_embedding_similarity
    EMBEDDING_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Embedding similarity not available: {e}")
    EMBEDDING_AVAILABLE = False

    def compute_embedding_similarity(text: str) -> Dict[str, Any]:
        return {
            "overall_similarity_score": 0.0,
            "criterion_scores": {},
            "embedding_available": False,
            "error": "Embedding module not available"
        }

load_dotenv()

# Define the rubric directly in this file to avoid import issues
RUBRIC = """
You are a scholarship judge evaluating personal statements for the Global Korea Scholarship (GKS) program.
You must evaluate essays based on specific criteria and return your assessment as JSON.

Return ONLY a JSON object in this exact format:
{
    "Introduction/Hook": {"score": <0-100>, "feedback": "<brief feedback>"},
    "Motivation": {"score": <0-100>, "feedback": "<brief feedback>"},
    "Educational Background": {"score": <0-100>, "feedback": "<brief feedback>"},
    "Extracurricular": {"score": <0-100>, "feedback": "<brief feedback>"}
}

## EVALUATION CRITERIA:

### 1. Introduction/Hook (0-100 points)
Evaluates the opening's ability to capture attention and set the tone.

**100-point example:**
"The first time I witnessed a child's face light up when they finally grasped a mathematical concept I had been teaching, I knew that education was not just my career choice—it was my calling. This moment, in a small tutoring center in rural Vietnam, crystallized my understanding that learning transcends language barriers and cultural differences."

**60-point example:**
"I have always been interested in computer science since I was young. My name is John and I want to study in Korea because it has good technology programs."

**20-point example:**
"Hello, my name is Sarah. I am writing this essay to apply for the scholarship. I hope you will consider my application."

### 2. Motivation & Objectives (0-100 points)
Assesses clarity of goals, connection to Korea, and future plans.

**100-point example:**
"My goal is to pursue a Master's in Artificial Intelligence at KAIST, specifically focusing on natural language processing for Southeast Asian languages. Korea's leadership in AI research, combined with companies like Samsung and LG pioneering human-computer interaction, makes it the ideal environment for my research. Upon completion, I plan to return to Thailand to establish an AI research lab that bridges Korean technological innovation with Southeast Asian linguistic diversity."

**60-point example:**
"I want to study computer science in Korea because Korean technology is advanced. After graduation, I plan to work in the tech industry and maybe start my own company."

**20-point example:**
"I want to study in Korea because I like K-pop and Korean culture. I think it would be fun to live there."

### 3. Educational Background (0-100 points)
Evaluates academic achievements, relevant coursework, and intellectual growth.

**100-point example:**
"During my undergraduate studies in Electrical Engineering at the University of Indonesia, I maintained a GPA of 3.85/4.0 while conducting research on renewable energy systems. My thesis on 'Solar Panel Efficiency Optimization Using Machine Learning' was published in the IEEE Indonesian Conference and earned the Best Undergraduate Research Award. Additionally, I completed advanced coursework in signal processing and control systems, which directly aligns with my graduate study goals."

**60-point example:**
"I graduated with a degree in Computer Science with a GPA of 3.2. I took courses in programming, databases, and software engineering. I also did a final project on web development."

**20-point example:**
"I studied business in college and got decent grades. I learned about management and marketing."

### 4. Extracurricular Activities (0-100 points)
Assesses leadership, community involvement, and character development.

**100-point example:**
"As president of the University Environmental Club, I led a team of 50 students in organizing the 'Green Campus Initiative,' which reduced university waste by 40% and installed solar panels across three dormitories. Additionally, I volunteered for two years teaching English to underprivileged children, founded a coding bootcamp for rural students that has trained over 200 participants, and represented my university in the ASEAN Youth Leadership Summit where I presented on sustainable technology solutions."

**60-point example:**
"I was a member of the student council and participated in some volunteer activities. I also played on the university basketball team for two years."

**20-point example:**
"I sometimes helped with school events and was in a few clubs during college."

## SCORING GUIDELINES:
- 90-100: Exceptional quality, demonstrates excellence
- 70-89: Strong quality, meets high standards
- 50-69: Adequate quality, meets basic requirements
- 30-49: Below average, needs significant improvement
- 0-29: Poor quality, major deficiencies

Evaluate the essay objectively based on these criteria and examples. Provide constructive feedback for each criterion.
"""

SYSTEM_PROMPT = "You are an expert scholarship evaluator. Analyze the provided essay according to the rubric and return only the requested JSON format."

def fast_rules(text: str) -> int:
    """
    Fast rule-based scoring for format compliance and basic requirements.
    Returns a score from 0-100 based on simple checks.
    """
    score = 100
    words = len(text.split())

    # Word count check (ideal range: 500-1000 words)
    if words < 300:
        score -= 30  # Too short
    elif words < 500:
        score -= 15  # Slightly short
    elif words > 1200:
        score -= 20  # Too long
    elif words > 1000:
        score -= 10  # Slightly long

    # Paragraph structure (should have multiple paragraphs)
    paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
    if len(paragraphs) < 3:
        score -= 15

    # Check for key elements
    text_lower = text.lower()

    # Should mention motivation/goals
    motivation_keywords = ['goal', 'objective', 'aim', 'aspire', 'dream', 'vision', 'plan']
    if not any(keyword in text_lower for keyword in motivation_keywords):
        score -= 15

    # Should mention Korea or Korean
    korea_keywords = ['korea', 'korean', 'seoul', 'busan', 'kaist', 'snu', 'yonsei']
    if not any(keyword in text_lower for keyword in korea_keywords):
        score -= 20

    # Should mention education/study
    education_keywords = ['study', 'education', 'learn', 'research', 'university', 'degree', 'academic']
    if not any(keyword in text_lower for keyword in education_keywords):
        score -= 10

    # Check for passive voice (too much is bad)
    passive_patterns = r'\b(is|was|were|be|been|being)\s+\w*ed\b'
    passive_matches = len(re.findall(passive_patterns, text, re.IGNORECASE))
    passive_ratio = passive_matches / words if words > 0 else 0
    if passive_ratio > 0.02:  # More than 2% passive voice
        score -= 10

    # Check for first person usage (should be present in personal statement)
    first_person = ['i ', 'my ', 'me ', 'myself']
    if not any(fp in text_lower for fp in first_person):
        score -= 15

    return max(0, min(100, score))

def enhanced_fast_rules(text: str) -> Dict[str, Any]:
    """
    Enhanced fast rule-based scoring with detailed analysis
    Returns comprehensive scoring breakdown
    """
    score = 100
    feedback_points = []
    details = {}

    # Basic validation
    if not text or not text.strip():
        return {
            "score": 0,
            "feedback": "Essay text cannot be empty",
            "details": {"error": "empty_text"}
        }

    # Word count analysis
    words = text.split()
    word_count = len(words)
    details["word_count"] = word_count

    if word_count < 300:
        score -= 30
        feedback_points.append(f"Essay too short ({word_count} words, recommended: 500-1000)")
    elif word_count < 500:
        score -= 15
        feedback_points.append(f"Essay slightly short ({word_count} words)")
    elif word_count > 1200:
        score -= 20
        feedback_points.append(f"Essay too long ({word_count} words)")
    elif word_count > 1000:
        score -= 10
        feedback_points.append(f"Essay slightly long ({word_count} words)")
    else:
        feedback_points.append(f"Good word count ({word_count} words)")

    # Paragraph structure
    paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
    paragraph_count = len(paragraphs)
    details["paragraph_count"] = paragraph_count

    if paragraph_count < 3:
        score -= 15
        feedback_points.append("Need more paragraphs for better structure")
    elif paragraph_count > 7:
        score -= 10
        feedback_points.append("Too many paragraphs, consider consolidating")
    else:
        feedback_points.append(f"Good paragraph structure ({paragraph_count} paragraphs)")

    # Keyword analysis
    text_lower = text.lower()
    keyword_results = {}

    # Korea keywords
    korea_keywords = ['korea', 'korean', 'seoul', 'busan', 'kaist', 'snu', 'yonsei']
    korea_found = [k for k in korea_keywords if k in text_lower]
    keyword_results["korea"] = korea_found
    if not korea_found:
        score -= 20
        feedback_points.append("Missing Korea-specific content")

    # Motivation keywords
    motivation_keywords = ['goal', 'objective', 'aim', 'aspire', 'dream', 'vision', 'plan']
    motivation_found = [k for k in motivation_keywords if k in text_lower]
    keyword_results["motivation"] = motivation_found
    if not motivation_found:
        score -= 15
        feedback_points.append("Unclear motivation or goals")

    # Education keywords
    education_keywords = ['study', 'education', 'learn', 'research', 'university', 'degree', 'academic']
    education_found = [k for k in education_keywords if k in text_lower]
    keyword_results["education"] = education_found
    if not education_found:
        score -= 10
        feedback_points.append("Limited educational background mentioned")

    details["keywords"] = keyword_results

    # Readability analysis
    sentences = re.split(r'[.!?]+', text)
    sentences = [s.strip() for s in sentences if s.strip()]

    if sentences and words:
        avg_words_per_sentence = len(words) / len(sentences)
        details["avg_words_per_sentence"] = round(avg_words_per_sentence, 2)

        if avg_words_per_sentence > 25:
            score -= 5
            feedback_points.append("Sentences are too long, consider breaking them up")
        elif avg_words_per_sentence < 10:
            score -= 5
            feedback_points.append("Sentences are too short, consider combining some")

    # Personal pronoun check
    first_person_count = len(re.findall(r'\b(I|me|my|myself|mine)\b', text, re.IGNORECASE))
    first_person_ratio = first_person_count / word_count if word_count > 0 else 0
    details["first_person_ratio"] = round(first_person_ratio, 3)

    if first_person_ratio < 0.02:  # Less than 2% first person
        score -= 15
        feedback_points.append("Personal statement should include more personal perspective")

    # Passive voice check
    passive_patterns = r'\b(is|was|were|be|been|being)\s+\w*ed\b'
    passive_matches = len(re.findall(passive_patterns, text, re.IGNORECASE))
    passive_ratio = passive_matches / word_count if word_count > 0 else 0
    details["passive_voice_ratio"] = round(passive_ratio, 3)

    if passive_ratio > 0.03:  # More than 3% passive voice
        score -= 10
        feedback_points.append("Too much passive voice, use more active voice")

    final_score = max(0, min(100, score))

    return {
        "score": final_score,
        "feedback": "; ".join(feedback_points) if feedback_points else "Good format compliance",
        "details": details
    }

def llm_scores(text: str) -> Dict[str, Any]:
    """
    Use OpenRouter API to get detailed criterion-based scores.
    Returns a dictionary with scores and feedback for each criterion.
    """
    api_key = os.getenv('OPENROUTER_API_KEY')
    base_url = os.getenv('OPENROUTER_BASE', 'https://openrouter.ai/api/v1')
    model_id = os.getenv('MODEL_ID', 'anthropic/claude-3.5-sonnet')

    if not api_key:
        raise ValueError("OPENROUTER_API_KEY not found in environment variables")

    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'HTTP-Referer': 'http://localhost:5000',
        'X-Title': 'Essay Evaluation Service'
    }

    prompt = f"{RUBRIC}\n\n### Essay to Evaluate:\n\"\"\"\n{text}\n\"\"\""

    payload = {
        'model': model_id,
        'messages': [
            {'role': 'system', 'content': SYSTEM_PROMPT},
            {'role': 'user', 'content': prompt}
        ],
        'temperature': 0,
        'max_tokens': 2000
    }

    try:
        response = requests.post(f'{base_url}/chat/completions',
                               headers=headers,
                               json=payload,
                               timeout=30)
        response.raise_for_status()

        result = response.json()
        content = result['choices'][0]['message']['content'].strip()

        # Extract JSON from the response
        json_start = content.find('{')
        json_end = content.rfind('}') + 1

        if json_start != -1 and json_end != -1:
            json_content = content[json_start:json_end]
            return json.loads(json_content)
        else:
            # Fallback parsing
            return json.loads(content)

    except requests.exceptions.RequestException as e:
        raise Exception(f"API request failed: {str(e)}")
    except json.JSONDecodeError as e:
        raise Exception(f"Failed to parse API response as JSON: {str(e)}")
    except Exception as e:
        raise Exception(f"Unexpected error in LLM scoring: {str(e)}")

def evaluate(text: str) -> Dict[str, Any]:
    """
    Main evaluation function that combines fast rules, LLM scores, and embedding similarity.
    Returns comprehensive evaluation results.
    """
    if not text or not text.strip():
        raise ValueError("Essay text cannot be empty")

    # Get enhanced fast rules score
    format_analysis = enhanced_fast_rules(text)

    # Get LLM scores
    try:
        llm_results = llm_scores(text)
        llm_available = True
    except Exception as e:
        # If LLM fails, return basic evaluation
        llm_results = {}
        llm_available = False
        llm_error = str(e)

    # Get embedding similarity scores
    try:
        embedding_results = compute_embedding_similarity(text)
        embedding_available = embedding_results.get("embedding_available", False)
    except Exception as e:
        embedding_results = {
            "overall_similarity_score": 0.0,
            "criterion_scores": {},
            "embedding_available": False,
            "error": str(e)
        }
        embedding_available = False

    # Combine results
    criteria = {
        "Format Compliance": {
            "score": format_analysis["score"],
            "feedback": format_analysis["feedback"]
        }
    }

    # Combine LLM and embedding scores for each criterion
    criterion_names = ["Introduction/Hook", "Motivation", "Educational Background", "Extracurricular"]

    for criterion in criterion_names:
        llm_score = None
        embedding_score = None
        combined_feedback = []

        # Get LLM score
        if llm_available and criterion in llm_results:
            llm_score = llm_results[criterion]["score"]
            combined_feedback.append(f"LLM: {llm_results[criterion]['feedback']}")

        # Get embedding similarity score
        if embedding_available and criterion in embedding_results.get("criterion_scores", {}):
            embedding_score = embedding_results["criterion_scores"][criterion]["score"]
            combined_feedback.append(f"Similarity: {embedding_results['criterion_scores'][criterion]['feedback']}")

        # Combine scores with weights
        if llm_score is not None and embedding_score is not None:
            # Both available: 70% LLM, 30% embedding similarity
            final_score = int(llm_score * 0.7 + embedding_score * 0.3)
        elif llm_score is not None:
            # Only LLM available
            final_score = llm_score
        elif embedding_score is not None:
            # Only embedding available
            final_score = embedding_score
        else:
            # Neither available, use format-based fallback
            final_score = max(50, format_analysis["score"] - 10)
            combined_feedback = ["Score based on format analysis only"]

        criteria[criterion] = {
            "score": final_score,
            "feedback": " | ".join(combined_feedback) if combined_feedback else "No detailed feedback available"
        }

    # Calculate overall score (weighted average)
    weights = {
        "Format Compliance": 0.20,
        "Introduction/Hook": 0.20,
        "Motivation": 0.25,
        "Educational Background": 0.20,
        "Extracurricular": 0.15
    }

    weighted_sum = sum(criteria[criterion]["score"] * weights.get(criterion, 0.2)
                      for criterion in criteria)
    overall_score = int(weighted_sum)

    result = {
        "overall": overall_score,
        "criteria": criteria,
        "word_count": len(text.split()),
        "evaluation_timestamp": datetime.datetime.now().isoformat(),
        "analysis_details": format_analysis.get("details", {}),
        "llm_available": llm_available,
        "embedding_available": embedding_available,
        "embedding_similarity": {
            "overall_score": embedding_results.get("overall_similarity_score", 0.0),
            "details": embedding_results.get("similarity_details", {})
        }
    }

    if not llm_available:
        result["llm_error"] = llm_error

    if not embedding_available and "error" in embedding_results:
        result["embedding_error"] = embedding_results["error"]

    return result
