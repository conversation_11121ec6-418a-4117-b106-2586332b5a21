events {
    worker_connections 1024;
}

http {
    upstream essay_evaluator {
        server essay-evaluator:5000;
    }

    server {
        listen 80;
        
        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/m;
        
        location / {
            limit_req zone=api burst=5 nodelay;
            
            proxy_pass http://essay_evaluator;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Timeouts
            proxy_connect_timeout 60s;
            proxy_send_timeout 60s;
            proxy_read_timeout 60s;
        }
        
        # Health check endpoint (no rate limiting)
        location /health {
            proxy_pass http://essay_evaluator;
            proxy_set_header Host $host;
        }
    }
}
