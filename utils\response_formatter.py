"""
Response formatting utilities for consistent API responses
"""

from typing import Any, Dict, Optional
import datetime

def format_success_response(data: Any, message: str = "Success", metadata: Optional[Dict] = None) -> Dict[str, Any]:
    """
    Format a successful API response
    """
    response = {
        "success": True,
        "message": message,
        "data": data,
        "timestamp": datetime.datetime.now().isoformat()
    }
    
    if metadata:
        response["metadata"] = metadata
    
    return response

def format_error_response(error: str, code: str, details: Optional[str] = None, 
                         suggestions: Optional[list] = None) -> Dict[str, Any]:
    """
    Format an error API response
    """
    response = {
        "success": False,
        "error": error,
        "code": code,
        "timestamp": datetime.datetime.now().isoformat()
    }
    
    if details:
        response["details"] = details
    
    if suggestions:
        response["suggestions"] = suggestions
    
    return response

def format_validation_error(validation_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format a validation error response
    """
    return format_error_response(
        error=validation_result.get("error", "Validation failed"),
        code="VALIDATION_ERROR",
        suggestions=[
            "Check that all required fields are provided",
            "Ensure data types are correct",
            "Verify field value constraints"
        ]
    )

def format_paginated_response(data: list, page: int, per_page: int, total: int, 
                            message: str = "Success") -> Dict[str, Any]:
    """
    Format a paginated API response
    """
    total_pages = (total + per_page - 1) // per_page  # Ceiling division
    
    return format_success_response(
        data=data,
        message=message,
        metadata={
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            }
        }
    )

def format_analysis_response(analysis_result: Dict[str, Any], analysis_type: str) -> Dict[str, Any]:
    """
    Format an analysis result response
    """
    return format_success_response(
        data=analysis_result,
        message=f"{analysis_type.title()} analysis completed successfully",
        metadata={
            "analysis_type": analysis_type,
            "features_used": list(analysis_result.keys()) if isinstance(analysis_result, dict) else []
        }
    )

def format_comparison_response(comparison_result: Dict[str, Any], comparison_type: str) -> Dict[str, Any]:
    """
    Format a comparison result response
    """
    return format_success_response(
        data=comparison_result,
        message=f"{comparison_type.title()} comparison completed successfully",
        metadata={
            "comparison_type": comparison_type,
            "items_compared": comparison_result.get("statistics", {}).get("total_essays", 0)
        }
    )

def format_service_status_response(service_name: str, status: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format a service status response
    """
    is_healthy = status.get("status") == "operational" or status.get("available", False)
    
    return {
        "success": True,
        "service": service_name,
        "status": "healthy" if is_healthy else "degraded",
        "details": status,
        "timestamp": datetime.datetime.now().isoformat()
    }

def add_response_headers(response_data: Dict[str, Any], headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """
    Add custom headers to response data
    """
    if headers:
        response_data["headers"] = headers
    
    return response_data

def format_rate_limit_error(limit: int, window: str, retry_after: int) -> Dict[str, Any]:
    """
    Format a rate limit error response
    """
    return format_error_response(
        error="Rate limit exceeded",
        code="RATE_LIMIT_EXCEEDED",
        details=f"Maximum {limit} requests per {window}",
        suggestions=[
            f"Wait {retry_after} seconds before retrying",
            "Consider reducing request frequency",
            "Contact support for higher rate limits"
        ]
    )

def format_maintenance_response(message: str = "Service temporarily unavailable") -> Dict[str, Any]:
    """
    Format a maintenance mode response
    """
    return format_error_response(
        error=message,
        code="SERVICE_MAINTENANCE",
        suggestions=[
            "Try again later",
            "Check service status page",
            "Contact support if issue persists"
        ]
    )
