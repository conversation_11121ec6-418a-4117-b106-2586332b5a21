@echo off
echo 📁 Creating organized folder structure for Essay Evaluation Service
echo ================================================================

REM Create main folders
echo Creating main application folders...
mkdir app 2>nul
mkdir core 2>nul
mkdir data\reference_essays\gks 2>nul
mkdir data\reference_essays\chevening 2>nul
mkdir data\reference_essays\fulbright 2>nul

REM Create test folders
echo Creating test folders...
mkdir tests\unit 2>nul
mkdir tests\integration 2>nul
mkdir tests\performance 2>nul

REM Create script folders
echo Creating script folders...
mkdir scripts\setup 2>nul
mkdir scripts\deployment 2>nul
mkdir scripts\testing 2>nul
mkdir scripts\maintenance 2>nul
mkdir scripts\demo 2>nul

REM Create docker folder
echo Creating docker folder...
mkdir docker 2>nul

REM Create documentation folders
echo Creating documentation folders...
mkdir docs\api 2>nul
mkdir docs\deployment 2>nul
mkdir docs\development 2>nul
mkdir docs\architecture 2>nul

REM Create other folders
echo Creating other folders...
mkdir logs 2>nul
mkdir uploads\temp 2>nul
mkdir static\css 2>nul
mkdir static\js 2>nul
mkdir static\images 2>nul
mkdir migrations\versions 2>nul

REM Create __init__.py files
echo Creating Python package files...
echo. > app\__init__.py
echo. > core\__init__.py
echo. > tests\__init__.py
echo. > tests\unit\__init__.py
echo. > tests\integration\__init__.py

echo.
echo ✅ Folder structure created successfully!
echo.
echo 📋 Suggested file organization:
echo.
echo 📁 app/                    - Main Flask application
echo    ├── app.py             - Main application file
echo    └── wsgi.py            - WSGI entry point
echo.
echo 📁 core/                  - Core business logic
echo    ├── evaluation.py      - Move evaluation.py here
echo    ├── embedding_similarity.py - Move embedding_similarity.py here
echo    ├── scholarship_criteria.py - Move scholarship_criteria.py here
echo    └── prompts.py         - Move prompts.py here
echo.
echo 📁 scripts/               - Utility scripts
echo    ├── setup/             - Move setup.bat, verify_setup.py here
echo    ├── deployment/        - Move deploy.sh here
echo    ├── testing/           - Move test_*.py, automate_test.py here
echo    ├── maintenance/       - Move cleanup_imports.py, validate_env.py here
echo    └── demo/              - Move demo*.py here
echo.
echo 📁 docker/                - Container configuration
echo    ├── Dockerfile         - Move Dockerfile here
echo    ├── docker-compose.yml - Move docker-compose.yml here
echo    └── nginx.conf         - Move nginx.conf here
echo.
echo 📁 docs/                  - Documentation
echo    ├── api/               - Move FRONTEND_INTEGRATION.md here
echo    ├── deployment/        - Move DEPLOYMENT_GUIDE.md here
echo    └── architecture/      - Move PROJECT_RESTRUCTURE.md here
echo.
echo 🚀 To complete organization:
echo 1. Move files to their suggested folders
echo 2. Update import statements in Python files
echo 3. Test the application: python app/app.py
echo.
pause
