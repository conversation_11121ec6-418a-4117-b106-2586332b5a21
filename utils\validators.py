"""
Input validation utilities
"""

from typing import Dict, Any

def validate_essay_input(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate essay input data
    """
    if not isinstance(data, dict):
        return {
            "valid": False,
            "error": "Request data must be a JSON object"
        }
    
    essay = data.get('essay', '')
    
    if not essay:
        return {
            "valid": False,
            "error": "Essay field is required"
        }
    
    if not isinstance(essay, str):
        return {
            "valid": False,
            "error": "Essay must be a string"
        }
    
    essay = essay.strip()
    
    if not essay:
        return {
            "valid": False,
            "error": "Essay cannot be empty"
        }
    
    if len(essay) > 50000:  # 50k character limit
        return {
            "valid": False,
            "error": "Essay is too long (maximum 50,000 characters)"
        }
    
    if len(essay) < 10:
        return {
            "valid": False,
            "error": "Essay is too short (minimum 10 characters)"
        }
    
    return {
        "valid": True,
        "essay": essay
    }

def validate_criteria_check_input(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate criteria check input data
    """
    if not isinstance(data, dict):
        return {
            "valid": False,
            "error": "Request data must be a JSON object"
        }
    
    # Validate essay text
    essay_validation = validate_essay_input(data)
    if not essay_validation["valid"]:
        return essay_validation
    
    # Validate scholarship type
    scholarship_type = data.get('scholarship_type', '').strip()
    if not scholarship_type:
        return {
            "valid": False,
            "error": "Scholarship type is required"
        }
    
    # Validate essay type
    essay_type = data.get('essay_type', '').strip()
    if not essay_type:
        return {
            "valid": False,
            "error": "Essay type is required"
        }
    
    # Validate scholarship type format
    valid_scholarships = ['gks', 'chevening', 'fulbright', 'daad', 'erasmus']
    if scholarship_type not in valid_scholarships:
        return {
            "valid": False,
            "error": f"Invalid scholarship type. Must be one of: {', '.join(valid_scholarships)}"
        }
    
    return {
        "valid": True,
        "text": essay_validation["essay"],
        "scholarship_type": scholarship_type,
        "essay_type": essay_type
    }

def validate_comparison_input(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate essay comparison input data
    """
    if not isinstance(data, dict):
        return {
            "valid": False,
            "error": "Request data must be a JSON object"
        }
    
    essays = data.get('essays', [])
    
    if not isinstance(essays, list):
        return {
            "valid": False,
            "error": "Essays must be an array"
        }
    
    if len(essays) < 2:
        return {
            "valid": False,
            "error": "At least 2 essays are required for comparison"
        }
    
    if len(essays) > 10:
        return {
            "valid": False,
            "error": "Maximum 10 essays allowed for comparison"
        }
    
    # Validate each essay
    validated_essays = []
    for i, essay in enumerate(essays):
        if not isinstance(essay, str):
            return {
                "valid": False,
                "error": f"Essay {i+1} must be a string"
            }
        
        essay = essay.strip()
        if not essay:
            return {
                "valid": False,
                "error": f"Essay {i+1} cannot be empty"
            }
        
        if len(essay) > 50000:
            return {
                "valid": False,
                "error": f"Essay {i+1} is too long (maximum 50,000 characters)"
            }
        
        validated_essays.append(essay)
    
    # Validate scholarship and essay type
    scholarship_type = data.get('scholarship_type', '').strip()
    essay_type = data.get('essay_type', '').strip()
    
    if not scholarship_type:
        return {
            "valid": False,
            "error": "Scholarship type is required"
        }
    
    if not essay_type:
        return {
            "valid": False,
            "error": "Essay type is required"
        }
    
    return {
        "valid": True,
        "essays": validated_essays,
        "scholarship_type": scholarship_type,
        "essay_type": essay_type
    }

def validate_reference_comparison_input(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate reference comparison input data
    """
    if not isinstance(data, dict):
        return {
            "valid": False,
            "error": "Request data must be a JSON object"
        }
    
    # Validate main essay
    essay_validation = validate_essay_input(data)
    if not essay_validation["valid"]:
        return essay_validation
    
    # Validate reference essays
    reference_essays = data.get('reference_essays', [])
    
    if not isinstance(reference_essays, list):
        return {
            "valid": False,
            "error": "Reference essays must be an array"
        }
    
    if not reference_essays:
        return {
            "valid": False,
            "error": "At least one reference essay is required"
        }
    
    if len(reference_essays) > 20:
        return {
            "valid": False,
            "error": "Maximum 20 reference essays allowed"
        }
    
    # Validate each reference essay
    validated_references = []
    for i, ref_essay in enumerate(reference_essays):
        if not isinstance(ref_essay, str):
            return {
                "valid": False,
                "error": f"Reference essay {i+1} must be a string"
            }
        
        ref_essay = ref_essay.strip()
        if not ref_essay:
            return {
                "valid": False,
                "error": f"Reference essay {i+1} cannot be empty"
            }
        
        if len(ref_essay) > 50000:
            return {
                "valid": False,
                "error": f"Reference essay {i+1} is too long (maximum 50,000 characters)"
            }
        
        validated_references.append(ref_essay)
    
    return {
        "valid": True,
        "essay": essay_validation["essay"],
        "reference_essays": validated_references
    }
