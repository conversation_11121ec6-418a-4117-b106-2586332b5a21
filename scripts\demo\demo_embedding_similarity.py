#!/usr/bin/env python3
"""
Demonstration of Embedding Similarity functionality
Shows how the Gemini-based embedding similarity enhances essay evaluation
"""

import requests
import json
import time
from typing import Dict, Any

class EmbeddingSimilarityDemo:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        
    def print_header(self, title):
        print(f"\n{'='*70}")
        print(f"{title.center(70)}")
        print(f"{'='*70}")
    
    def print_section(self, title):
        print(f"\n{'-'*50}")
        print(f"{title}")
        print(f"{'-'*50}")
    
    def check_service_health(self):
        """Check if the service is running"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Service is healthy: {data['service']} v{data['version']}")
                return True
            else:
                print(f"❌ Service health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Cannot connect to service: {e}")
            print(f"   Make sure the service is running at {self.base_url}")
            return False
    
    def test_similarity_endpoint(self, essay: str, title: str) -> Dict[str, Any]:
        """Test the similarity endpoint specifically"""
        try:
            response = requests.post(f"{self.base_url}/api/v1/similarity",
                                   json={"essay": essay},
                                   headers={"Content-Type": "application/json"},
                                   timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Similarity analysis failed for {title}: {response.status_code}")
                print(f"   Response: {response.text}")
                return None
        except Exception as e:
            print(f"❌ Error in similarity analysis for {title}: {e}")
            return None
    
    def test_full_evaluation(self, essay: str, title: str) -> Dict[str, Any]:
        """Test the full evaluation with embedding similarity"""
        try:
            response = requests.post(f"{self.base_url}/api/v1/evaluate",
                                   json={"essay": essay},
                                   headers={"Content-Type": "application/json"},
                                   timeout=60)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Full evaluation failed for {title}: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ Error in full evaluation for {title}: {e}")
            return None
    
    def display_similarity_results(self, results: Dict[str, Any], title: str):
        """Display similarity analysis results"""
        if not results:
            return
        
        similarity_analysis = results.get("similarity_analysis", {})
        
        print(f"\n📊 Similarity Analysis for: {title}")
        print(f"Overall Similarity Score: {similarity_analysis.get('overall_similarity_score', 0):.1f}/100")
        print(f"Embedding Available: {similarity_analysis.get('embedding_available', False)}")
        
        if similarity_analysis.get("criterion_scores"):
            print("\nCriterion Similarity Scores:")
            for criterion, score_data in similarity_analysis["criterion_scores"].items():
                score = score_data["score"]
                feedback = score_data["feedback"]
                print(f"  • {criterion}: {score:.1f}/100")
                print(f"    {feedback}")
        
        if similarity_analysis.get("similarity_details"):
            print("\nDetailed Similarity Metrics:")
            for criterion, details in similarity_analysis["similarity_details"].items():
                print(f"  • {criterion}:")
                print(f"    Max similarity: {details['max_similarity']:.3f}")
                print(f"    Mean similarity: {details['mean_similarity']:.3f}")
                print(f"    References used: {details['num_references']}")
    
    def display_full_evaluation_results(self, results: Dict[str, Any], title: str):
        """Display full evaluation results with embedding integration"""
        if not results:
            return
        
        print(f"\n📈 Full Evaluation Results for: {title}")
        print(f"Overall Score: {results['overall']}/100")
        print(f"Word Count: {results['word_count']}")
        print(f"LLM Available: {results.get('llm_available', False)}")
        print(f"Embedding Available: {results.get('embedding_available', False)}")
        
        # Show embedding similarity summary
        embedding_sim = results.get('embedding_similarity', {})
        if embedding_sim:
            print(f"Embedding Similarity Score: {embedding_sim.get('overall_score', 0):.1f}/100")
        
        print("\nCriteria Breakdown (Combined LLM + Embedding):")
        for criterion, details in results['criteria'].items():
            score = details['score']
            feedback = details['feedback']
            print(f"  • {criterion}: {score}/100")
            # Show if feedback contains both LLM and similarity components
            if " | " in feedback:
                parts = feedback.split(" | ")
                for i, part in enumerate(parts):
                    print(f"    {i+1}. {part}")
            else:
                print(f"    {feedback}")
    
    def run_demo(self):
        """Run the complete embedding similarity demonstration"""
        self.print_header("EMBEDDING SIMILARITY DEMONSTRATION")
        
        # Check service health
        if not self.check_service_health():
            return
        
        # Sample essays with different quality levels
        essays = {
            "High-Quality Essay (Similar to References)": """
            The first time I witnessed a child's face light up when they finally grasped a mathematical concept I had been teaching, I knew that education was not just my career choice—it was my calling. This moment, in a small tutoring center in rural Vietnam, crystallized my understanding that learning transcends language barriers and cultural differences.

            My goal is to pursue a Master's in Artificial Intelligence at KAIST, specifically focusing on natural language processing for Southeast Asian languages. Korea's leadership in AI research, combined with companies like Samsung and LG pioneering human-computer interaction, makes it the ideal environment for my research. Upon completion, I plan to return to Thailand to establish an AI research lab that bridges Korean technological innovation with Southeast Asian linguistic diversity.

            During my undergraduate studies in Electrical Engineering at the University of Indonesia, I maintained a GPA of 3.85/4.0 while conducting research on renewable energy systems. My thesis on 'Solar Panel Efficiency Optimization Using Machine Learning' was published in the IEEE Indonesian Conference and earned the Best Undergraduate Research Award.

            As president of the University Environmental Club, I led a team of 50 students in organizing the 'Green Campus Initiative,' which reduced university waste by 40% and installed solar panels across three dormitories. Additionally, I volunteered weekly at a local orphanage, teaching English to children ages 8-12.
            """,
            
            "Medium-Quality Essay": """
            I have always been interested in technology and computers since I was young. This interest grew stronger during my university studies where I learned about programming and software development.

            My goal is to study computer science in Korea because Korean universities are well-known for their technology programs. I want to learn more about artificial intelligence and machine learning. After graduation, I hope to work for a technology company.

            During my studies at the university, I maintained good grades and completed several programming projects. I also participated in some coding competitions and learned different programming languages like Python and Java.

            I was involved in the computer science club at my university and helped organize some events. I also did some volunteer work teaching basic computer skills to elderly people in my community.
            """,
            
            "Low-Quality Essay": """
            I want to study in Korea because I like Korean culture and K-pop. Korea seems like a cool place to live and study.

            I want to get a degree in computer science. I think computers are interesting and there are many job opportunities in this field.

            I studied computer science in college and got okay grades. I learned some programming but it was difficult sometimes.

            I was in some clubs during college and helped with a few activities. I also worked part-time at a computer store.
            """
        }
        
        # Test each essay
        for title, essay in essays.items():
            self.print_section(f"Testing: {title}")
            print(f"Essay length: {len(essay.split())} words")
            
            # Test similarity endpoint specifically
            print("\n1. Similarity Analysis Only:")
            start_time = time.time()
            similarity_results = self.test_similarity_endpoint(essay, title)
            similarity_time = time.time() - start_time
            
            if similarity_results:
                self.display_similarity_results(similarity_results, title)
                print(f"⏱️ Similarity analysis time: {similarity_time:.2f} seconds")
            
            # Test full evaluation with embedding integration
            print("\n2. Full Evaluation (LLM + Embedding):")
            start_time = time.time()
            full_results = self.test_full_evaluation(essay, title)
            full_time = time.time() - start_time
            
            if full_results:
                self.display_full_evaluation_results(full_results, title)
                print(f"⏱️ Full evaluation time: {full_time:.2f} seconds")
        
        # Summary and comparison
        self.print_section("EMBEDDING SIMILARITY BENEFITS")
        print("🎯 Key Advantages of Embedding Similarity:")
        print("  • Semantic understanding beyond keyword matching")
        print("  • Comparison against high-quality reference essays")
        print("  • Consistent scoring based on proven examples")
        print("  • Cost-effective using Google Gemini API")
        print("  • Complements LLM evaluation for better accuracy")
        
        print("\n📊 How It Works:")
        print("  1. Extract essay sections (intro, motivation, education, activities)")
        print("  2. Generate embeddings using Google Gemini")
        print("  3. Compare with reference essay embeddings")
        print("  4. Calculate cosine similarity scores")
        print("  5. Combine with LLM scores (70% LLM + 30% similarity)")
        
        print("\n🔧 Configuration:")
        print("  • Add GEMINI_API_KEY to .env file")
        print("  • Install: pip install google-generativeai numpy scikit-learn")
        print("  • Reference essays stored in data/winners.json")
        print("  • Embeddings cached to reduce API costs")
        
        self.print_header("DEMONSTRATION COMPLETE")
        print("🎉 Embedding similarity is working and enhancing evaluations!")
        print(f"🌐 Service URL: {self.base_url}")
        print("📚 For setup instructions, see test_embedding_similarity.py")

if __name__ == "__main__":
    demo = EmbeddingSimilarityDemo()
    demo.run_demo()
