"""
Health check routes
"""

from flask import Blueprint, jsonify
import datetime

health_bp = Blueprint('health', __name__)

@health_bp.route('/health', methods=['GET'])
def health_check():
    """
    Health check endpoint
    """
    return jsonify({
        "status": "healthy",
        "service": "Essay Evaluation Microservice",
        "version": "2.0.0",
        "timestamp": datetime.datetime.now().isoformat(),
        "features": {
            "evaluation": True,
            "embedding_similarity": True,
            "scholarship_criteria": True,
            "multiple_scholarships": True
        }
    }), 200

@health_bp.route('/status', methods=['GET'])
def service_status():
    """
    Detailed service status
    """
    try:
        # Check if core services are available
        from services.evaluation_service import EvaluationService
        from services.scholarship_service import ScholarshipService
        
        eval_service = EvaluationService()
        scholarship_service = ScholarshipService()
        
        status = {
            "service": "Essay Evaluation Microservice",
            "version": "2.0.0",
            "status": "operational",
            "timestamp": datetime.datetime.now().isoformat(),
            "components": {
                "evaluation_engine": "operational",
                "embedding_similarity": "operational" if eval_service.embedding_available else "limited",
                "scholarship_criteria": "operational",
                "llm_integration": "operational" if eval_service.llm_available else "limited"
            },
            "statistics": {
                "supported_scholarships": len(scholarship_service.get_scholarships()),
                "total_essay_types": sum(len(scholarship_service.get_essay_types(s['id'])) 
                                       for s in scholarship_service.get_scholarships())
            }
        }
        
        return jsonify(status), 200
        
    except Exception as e:
        return jsonify({
            "service": "Essay Evaluation Microservice",
            "status": "degraded",
            "error": str(e),
            "timestamp": datetime.datetime.now().isoformat()
        }), 503
