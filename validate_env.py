#!/usr/bin/env python3
"""
Environment validation script for Essay Evaluation Service
Validates .env configuration and API connectivity
"""

import os
import sys
import requests
import json
from dotenv import load_dotenv

def print_success(message):
    try:
        print(f"✅ {message}")
    except UnicodeEncodeError:
        print(f"[SUCCESS] {message}")

def print_error(message):
    try:
        print(f"❌ {message}")
    except UnicodeEncodeError:
        print(f"[ERROR] {message}")

def print_warning(message):
    try:
        print(f"⚠️  {message}")
    except UnicodeEncodeError:
        print(f"[WARNING] {message}")

def print_info(message):
    try:
        print(f"ℹ️  {message}")
    except UnicodeEncodeError:
        print(f"[INFO] {message}")

def validate_env_file():
    """Validate .env file exists and has required variables"""
    try:
        print("🔍 Validating .env configuration...")
    except UnicodeEncodeError:
        print("[VALIDATING] .env configuration...")
    print("-" * 40)

    if not os.path.exists('.env'):
        print_error(".env file not found")
        print_info("Create a .env file with the following variables:")
        print("OPENROUTER_API_KEY=your_api_key_here")
        print("OPENROUTER_BASE=https://openrouter.ai/api/v1")
        print("MODEL_ID=anthropic/claude-3.5-sonnet")
        return False

    print_success(".env file exists")

    # Load environment variables
    load_dotenv()

    # Check required variables
    required_vars = {
        'OPENROUTER_API_KEY': 'OpenRouter API key for LLM evaluation',
        'OPENROUTER_BASE': 'OpenRouter API base URL',
        'MODEL_ID': 'AI model identifier'
    }

    # Check optional variables
    optional_vars = {
        'GEMINI_API_KEY': 'Google Gemini API key for embedding similarity (optional)'
    }

    all_valid = True

    for var, description in required_vars.items():
        value = os.getenv(var)
        if value:
            if var == 'OPENROUTER_API_KEY':
                if value == 'your_openrouter_api_key_here' or value.startswith('sk-or-v1-') == False:
                    print_warning(f"{var} appears to be a placeholder or invalid format")
                    print_info("Get your API key from https://openrouter.ai/")
                    all_valid = False
                else:
                    print_success(f"{var} is set (key: {value[:10]}...)")
            else:
                print_success(f"{var} = {value}")
        else:
            print_error(f"{var} is not set - {description}")
            all_valid = False

    # Check optional variables
    for var, description in optional_vars.items():
        value = os.getenv(var)
        if value and value != 'your_gemini_api_key_here':
            print_success(f"{var} is configured (enhances embedding similarity)")
        else:
            print_warning(f"{var} not configured - {description}")

    return all_valid

def test_api_connectivity():
    """Test API connectivity with OpenRouter"""
    try:
        print("\n🌐 Testing API connectivity...")
    except UnicodeEncodeError:
        print("\n[TESTING] API connectivity...")
    print("-" * 40)

    load_dotenv()

    api_key = os.getenv('OPENROUTER_API_KEY')
    base_url = os.getenv('OPENROUTER_BASE', 'https://openrouter.ai/api/v1')
    model_id = os.getenv('MODEL_ID', 'anthropic/claude-3.5-sonnet')

    if not api_key or api_key == 'your_openrouter_api_key_here':
        print_warning("API key not configured - skipping connectivity test")
        return False

    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'HTTP-Referer': 'http://localhost:5000',
        'X-Title': 'Essay Evaluation Service Test'
    }

    # Test with a simple prompt
    payload = {
        'model': model_id,
        'messages': [
            {'role': 'system', 'content': 'You are a helpful assistant.'},
            {'role': 'user', 'content': 'Say "API test successful" if you can read this.'}
        ],
        'temperature': 0,
        'max_tokens': 50
    }

    try:
        print_info(f"Testing connection to {base_url}")
        print_info(f"Using model: {model_id}")

        response = requests.post(f'{base_url}/chat/completions',
                               headers=headers,
                               json=payload,
                               timeout=30)

        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print_success(f"API connection successful")
            print_info(f"Response: {content}")
            return True
        else:
            print_error(f"API request failed: {response.status_code}")
            print_info(f"Response: {response.text}")
            return False

    except requests.exceptions.Timeout:
        print_error("API request timed out")
        return False
    except requests.exceptions.ConnectionError:
        print_error("Could not connect to API")
        return False
    except Exception as e:
        print_error(f"API test failed: {str(e)}")
        return False

def test_evaluation_function():
    """Test the evaluation function with current configuration"""
    try:
        print("\n🧪 Testing evaluation function...")
    except UnicodeEncodeError:
        print("\n[TESTING] evaluation function...")
    print("-" * 40)

    try:
        from evaluation import evaluate

        test_essay = """
        I am passionate about studying artificial intelligence in Korea because of its advanced technology sector.
        My goal is to pursue a Master's degree at KAIST to research machine learning applications.

        During my undergraduate studies in Computer Science, I maintained a GPA of 3.8/4.0 and published research papers.
        I have experience in programming, data analysis, and software development.

        As president of the coding club, I organized hackathons and mentored junior students.
        I also volunteered at local schools teaching programming to children.

        After graduation, I plan to return to my home country and establish a tech startup
        that leverages Korean AI innovations to solve local problems.
        """

        print_info("Running evaluation test...")
        result = evaluate(test_essay)

        if isinstance(result, dict) and 'overall' in result:
            print_success("Evaluation function works correctly")
            print_info(f"Overall score: {result['overall']}/100")
            print_info(f"Word count: {result['word_count']}")
            print_info(f"LLM available: {result.get('llm_available', 'Unknown')}")

            # Show criteria scores
            if 'criteria' in result:
                print_info("Criteria scores:")
                for criterion, data in result['criteria'].items():
                    print(f"  • {criterion}: {data['score']}/100")

            return True
        else:
            print_error("Evaluation function returned invalid result")
            return False

    except Exception as e:
        print_error(f"Evaluation test failed: {str(e)}")
        return False

def main():
    """Main validation function"""
    try:
        print("🔧 Essay Evaluation Service - Environment Validation")
    except UnicodeEncodeError:
        print("Essay Evaluation Service - Environment Validation")
    print("=" * 60)

    # Run all validations
    env_valid = validate_env_file()
    api_valid = test_api_connectivity()
    eval_valid = test_evaluation_function()

    # Summary
    try:
        print("\n📋 Validation Summary")
    except UnicodeEncodeError:
        print("\nValidation Summary")
    print("=" * 60)

    if env_valid:
        print_success("Environment configuration is valid")
    else:
        print_error("Environment configuration has issues")

    if api_valid:
        print_success("API connectivity is working")
    else:
        print_warning("API connectivity failed - LLM features will be limited")

    if eval_valid:
        print_success("Evaluation function is working")
    else:
        print_error("Evaluation function has issues")

    # Overall status
    try:
        print("\n🎯 Overall Status")
    except UnicodeEncodeError:
        print("\nOverall Status")
    print("-" * 20)

    if env_valid and eval_valid:
        if api_valid:
            try:
                print_success("🎉 All systems ready! Full functionality available.")
            except UnicodeEncodeError:
                print_success("All systems ready! Full functionality available.")
        else:
            try:
                print_warning("⚡ Basic functionality ready. LLM features limited.")
            except UnicodeEncodeError:
                print_warning("Basic functionality ready. LLM features limited.")
        print_info("You can now run: python app.py")
        return True
    else:
        try:
            print_error("❌ System not ready. Please fix the issues above.")
        except UnicodeEncodeError:
            print_error("System not ready. Please fix the issues above.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
