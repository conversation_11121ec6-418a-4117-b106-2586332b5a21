#!/usr/bin/env python3
"""
Automated Project Reorganization Script
Reorganizes the essay evaluation project into a clean folder structure
"""

import os
import shutil
import sys
from pathlib import Path

class ProjectReorganizer:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.backup_dir = self.project_root / "backup_before_reorganization"
        
    def create_backup(self):
        """Create backup of current state"""
        print("📦 Creating backup of current project state...")
        
        if self.backup_dir.exists():
            shutil.rmtree(self.backup_dir)
        
        # Copy all files to backup (except existing organized folders)
        ignore_patterns = shutil.ignore_patterns(
            '__pycache__', '*.pyc', '.git', 'node_modules', 
            'routes', 'services', 'models', 'utils', 'config'
        )
        
        shutil.copytree(
            self.project_root, 
            self.backup_dir,
            ignore=ignore_patterns,
            dirs_exist_ok=True
        )
        print(f"✅ Backup created at: {self.backup_dir}")
    
    def create_folder_structure(self):
        """Create the new folder structure"""
        print("📁 Creating new folder structure...")
        
        folders = [
            "app",
            "core", 
            "data/reference_essays/gks",
            "data/reference_essays/chevening", 
            "data/reference_essays/fulbright",
            "tests/unit",
            "tests/integration", 
            "tests/performance",
            "scripts/setup",
            "scripts/deployment",
            "scripts/testing", 
            "scripts/maintenance",
            "scripts/demo",
            "docker",
            "docs/api",
            "docs/deployment",
            "docs/development", 
            "docs/architecture",
            "logs",
            "uploads/temp",
            "static/css",
            "static/js", 
            "static/images",
            "migrations/versions"
        ]
        
        for folder in folders:
            folder_path = self.project_root / folder
            folder_path.mkdir(parents=True, exist_ok=True)
            
            # Create __init__.py for Python packages
            if folder in ["app", "core", "tests", "tests/unit", "tests/integration"]:
                init_file = folder_path / "__init__.py"
                if not init_file.exists():
                    init_file.touch()
        
        print("✅ Folder structure created")
    
    def move_files(self):
        """Move files to their new locations"""
        print("🚚 Moving files to organized structure...")
        
        # File movement mapping
        moves = {
            # Main application
            "app_new.py": "app/app.py",
            "app.py": "app/app_legacy.py",
            
            # Core business logic
            "evaluation.py": "core/evaluation.py",
            "embedding_similarity.py": "core/embedding_similarity.py", 
            "scholarship_criteria.py": "core/scholarship_criteria.py",
            "prompts.py": "core/prompts.py",
            "utils.py": "utils/file_helpers.py",
            
            # Test files
            "test_restructured_api.py": "tests/integration/test_restructured_api.py",
            "test_scholarship_criteria.py": "tests/integration/test_scholarship_criteria.py",
            "test_embedding_similarity.py": "tests/integration/test_embedding_similarity.py",
            "automate_test.py": "scripts/testing/automate_test.py",
            
            # Demo files
            "demo.py": "scripts/demo/demo.py",
            "demo_embedding_similarity.py": "scripts/demo/demo_embedding_similarity.py",
            
            # Deployment files
            "deploy.sh": "scripts/deployment/deploy.sh",
            "docker-compose.yml": "docker/docker-compose.yml",
            "Dockerfile": "docker/Dockerfile", 
            "nginx.conf": "docker/nginx.conf",
            
            # Setup and maintenance
            "setup.bat": "scripts/setup/setup.bat",
            "run.bat": "scripts/setup/run.bat",
            "test_and_run.bat": "scripts/testing/test_and_run.bat",
            "validate_env.py": "scripts/maintenance/validate_env.py",
            "verify_setup.py": "scripts/setup/verify_setup.py",
            "cleanup_imports.py": "scripts/maintenance/cleanup_imports.py",
            "compute_centroids.py": "scripts/maintenance/compute_centroids.py",
            
            # Documentation
            "DEPLOYMENT_GUIDE.md": "docs/deployment/DEPLOYMENT_GUIDE.md",
            "PROJECT_RESTRUCTURE.md": "docs/architecture/PROJECT_RESTRUCTURE.md", 
            "FOLDER_ORGANIZATION.md": "docs/architecture/FOLDER_ORGANIZATION.md",
            "FRONTEND_INTEGRATION.md": "docs/api/FRONTEND_INTEGRATION.md",
        }
        
        for source, destination in moves.items():
            source_path = self.project_root / source
            dest_path = self.project_root / destination
            
            if source_path.exists():
                # Create destination directory if it doesn't exist
                dest_path.parent.mkdir(parents=True, exist_ok=True)
                
                # Move the file
                shutil.move(str(source_path), str(dest_path))
                print(f"  ✅ Moved {source} → {destination}")
            else:
                print(f"  ⚠️  File not found: {source}")
        
        print("✅ File movement completed")
    
    def create_new_files(self):
        """Create new configuration and helper files"""
        print("📝 Creating new configuration files...")
        
        # Create .env.example
        env_file = self.project_root / ".env"
        env_example = self.project_root / ".env.example"
        
        if env_file.exists() and not env_example.exists():
            with open(env_file, 'r') as f:
                content = f.read()
            
            # Replace actual values with placeholders
            example_content = content.replace(
                os.getenv('OPENROUTER_API_KEY', ''), 'your_openrouter_api_key_here'
            ).replace(
                os.getenv('GEMINI_API_KEY', ''), 'your_gemini_api_key_here'
            )
            
            with open(env_example, 'w') as f:
                f.write(example_content)
            print("  ✅ Created .env.example")
        
        # Create requirements-dev.txt
        req_dev = self.project_root / "requirements-dev.txt"
        if not req_dev.exists():
            dev_requirements = """# Development dependencies
pytest>=7.4.3
pytest-flask>=1.3.0
pytest-cov>=4.1.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0
pre-commit>=3.0.0
"""
            with open(req_dev, 'w') as f:
                f.write(dev_requirements)
            print("  ✅ Created requirements-dev.txt")
        
        # Create Makefile
        makefile = self.project_root / "Makefile"
        if not makefile.exists():
            makefile_content = """.PHONY: install test run clean deploy

install:
	pip install -r requirements.txt
	pip install -r requirements-dev.txt

test:
	python -m pytest tests/ -v

test-unit:
	python -m pytest tests/unit/ -v

test-integration:
	python -m pytest tests/integration/ -v

run:
	python app/app.py

run-dev:
	FLASK_ENV=development python app/app.py

clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete

deploy:
	./scripts/deployment/deploy.sh

setup:
	./scripts/setup/setup.sh

validate:
	python scripts/maintenance/validate_env.py
"""
            with open(makefile, 'w') as f:
                f.write(makefile_content)
            print("  ✅ Created Makefile")
        
        # Create main README.md if it doesn't exist
        readme = self.project_root / "README.md"
        if not readme.exists():
            readme_content = """# Essay Evaluation Service

Advanced AI-powered essay evaluation system with embedding similarity and scholarship-specific criteria.

## Quick Start

```bash
# Install dependencies
make install

# Run the service
make run

# Run tests
make test
```

## Documentation

- [API Documentation](docs/api/)
- [Deployment Guide](docs/deployment/)
- [Development Setup](docs/development/)
- [Architecture Overview](docs/architecture/)

## Features

- ✅ AI-powered essay evaluation
- ✅ Embedding similarity analysis
- ✅ Multiple scholarship support
- ✅ RESTful API design
- ✅ Comprehensive testing
"""
            with open(readme, 'w') as f:
                f.write(readme_content)
            print("  ✅ Created README.md")
        
        print("✅ New files created")
    
    def update_imports(self):
        """Update import statements in moved files"""
        print("🔧 Updating import statements...")
        
        # Files that need import updates
        files_to_update = [
            "app/app.py",
            "routes/evaluation.py",
            "routes/scholarship.py", 
            "routes/similarity.py",
            "services/evaluation_service.py",
            "services/scholarship_service.py",
            "services/similarity_service.py"
        ]
        
        for file_path in files_to_update:
            full_path = self.project_root / file_path
            if full_path.exists():
                try:
                    with open(full_path, 'r') as f:
                        content = f.read()
                    
                    # Update imports to use new structure
                    updated_content = content.replace(
                        'from evaluation import', 'from core.evaluation import'
                    ).replace(
                        'from embedding_similarity import', 'from core.embedding_similarity import'
                    ).replace(
                        'from scholarship_criteria import', 'from core.scholarship_criteria import'
                    )
                    
                    with open(full_path, 'w') as f:
                        f.write(updated_content)
                    
                    print(f"  ✅ Updated imports in {file_path}")
                except Exception as e:
                    print(f"  ⚠️  Could not update {file_path}: {e}")
        
        print("✅ Import updates completed")
    
    def reorganize(self):
        """Run the complete reorganization process"""
        print("🚀 Starting project reorganization...")
        print("=" * 60)
        
        try:
            self.create_backup()
            self.create_folder_structure()
            self.move_files()
            self.create_new_files()
            self.update_imports()
            
            print("=" * 60)
            print("🎉 Project reorganization completed successfully!")
            print(f"📦 Backup available at: {self.backup_dir}")
            print("\n📋 Next steps:")
            print("1. Test the reorganized structure: python app/app.py")
            print("2. Run tests: make test")
            print("3. Update any remaining import statements if needed")
            print("4. Review and commit changes")
            
        except Exception as e:
            print(f"❌ Error during reorganization: {e}")
            print(f"📦 Restore from backup: {self.backup_dir}")
            sys.exit(1)

if __name__ == "__main__":
    reorganizer = ProjectReorganizer()
    
    # Confirm before proceeding
    response = input("🤔 This will reorganize your project structure. Continue? (y/N): ")
    if response.lower() != 'y':
        print("❌ Reorganization cancelled")
        sys.exit(0)
    
    reorganizer.reorganize()
