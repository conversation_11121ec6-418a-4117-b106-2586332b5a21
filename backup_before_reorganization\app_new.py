#!/usr/bin/env python3
"""
Essay Evaluation Service - Main Application
Restructured with Flask Blueprints and REST API architecture
"""

from flask import Flask, jsonify
from flask_cors import CORS
import os
import logging
from config.settings import get_config

def create_app(config_name=None):
    """Application factory pattern"""
    app = Flask(__name__)

    # Load configuration
    if config_name is None:
        config_name = os.getenv('FLASK_ENV', 'development')

    config_class = get_config()
    app.config.from_object(config_class)

    # Enable CORS
    CORS(app, origins=app.config['CORS_ORIGINS'])

    # Configure logging
    logging.basicConfig(
        level=getattr(logging, app.config['LOG_LEVEL']),
        format='%(asctime)s %(levelname)s: %(message)s'
    )

    # Create data directories if they don't exist
    os.makedirs(app.config['DATA_DIR'], exist_ok=True)
    os.makedirs(app.config['CACHE_DIR'], exist_ok=True)

    # Import and register blueprints
    from routes.health import health_bp
    from routes.evaluation import evaluation_bp
    from routes.scholarship import scholarship_bp
    from routes.similarity import similarity_bp
    from routes.analysis import analysis_bp

    # Register blueprints with API versioning
    app.register_blueprint(health_bp, url_prefix='/')
    app.register_blueprint(evaluation_bp, url_prefix='/api/v1/evaluation')
    app.register_blueprint(scholarship_bp, url_prefix='/api/v1/scholarship')
    app.register_blueprint(similarity_bp, url_prefix='/api/v1/similarity')
    app.register_blueprint(analysis_bp, url_prefix='/api/v1/analysis')

    # Global error handlers
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({
            "success": False,
            "error": "Endpoint not found",
            "code": "NOT_FOUND",
            "available_endpoints": [
                "GET /health - Service health check",
                "GET /status - Detailed service status",
                "GET /api/v1/evaluation/criteria - Get evaluation criteria",
                "POST /api/v1/evaluation/evaluate - Comprehensive essay evaluation",
                "POST /api/v1/evaluation/quick-score - Quick essay scoring",
                "POST /api/v1/analysis/structure - Structure analysis",
                "POST /api/v1/analysis/readability - Readability analysis",
                "POST /api/v1/analysis/comprehensive - Full text analysis",
                "POST /api/v1/similarity/analyze - Embedding similarity analysis",
                "POST /api/v1/similarity/compare-references - Compare with references",
                "GET /api/v1/similarity/status - Similarity service status",
                "GET /api/v1/scholarship/list - Get all scholarships",
                "GET /api/v1/scholarship/<id>/essays - Get essay types for scholarship",
                "POST /api/v1/scholarship/criteria-check - Scholarship criteria evaluation",
                "POST /api/v1/scholarship/compare - Compare multiple essays"
            ]
        }), 404

    @app.errorhandler(405)
    def method_not_allowed(error):
        return jsonify({
            "success": False,
            "error": "Method not allowed",
            "code": "METHOD_NOT_ALLOWED"
        }), 405

    @app.errorhandler(500)
    def internal_error(error):
        app.logger.error(f"Internal server error: {error}")
        return jsonify({
            "success": False,
            "error": "Internal server error",
            "code": "INTERNAL_ERROR"
        }), 500

    @app.errorhandler(413)
    def request_entity_too_large(error):
        return jsonify({
            "success": False,
            "error": "Request entity too large",
            "code": "PAYLOAD_TOO_LARGE",
            "max_size": "16MB"
        }), 413

    # Add API info endpoint
    @app.route('/api/info', methods=['GET'])
    def api_info():
        """Get API information"""
        return jsonify({
            "success": True,
            "api": app.config.get_api_info(),
            "features": app.config.get_feature_flags(),
            "endpoints": {
                "health": "/health",
                "evaluation": "/api/v1/evaluation/*",
                "scholarship": "/api/v1/scholarship/*",
                "similarity": "/api/v1/similarity/*",
                "analysis": "/api/v1/analysis/*"
            }
        })

    return app

# Create application instance
app = create_app()

if __name__ == '__main__':
    config_obj = get_config()()  # Get config instance
    print(f"🚀 Starting {config_obj.SERVICE_NAME} v{config_obj.SERVICE_VERSION}")
    print(f"📊 Environment: {os.getenv('FLASK_ENV', 'development')}")
    print(f"🔧 Debug mode: {config_obj.DEBUG}")
    print(f"🌐 CORS origins: {config_obj.CORS_ORIGINS}")
    print(f"✨ Features enabled:")
    for feature, enabled in config_obj.get_feature_flags().items():
        status = "✅" if enabled else "❌"
        print(f"   {status} {feature}")
    print(f"🎯 Server starting on http://0.0.0.0:5000")

    app.run(
        host='0.0.0.0',
        port=5000,
        debug=config_obj.DEBUG,
        threaded=True
    )
