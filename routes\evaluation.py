"""
Essay evaluation routes
"""

from flask import Blueprint, request, jsonify, current_app
from services.evaluation_service import EvaluationService
from utils.validators import validate_essay_input
from utils.response_formatter import format_success_response, format_error_response

evaluation_bp = Blueprint('evaluation', __name__)

@evaluation_bp.route('/criteria', methods=['GET'])
def get_evaluation_criteria():
    """
    Get evaluation criteria information
    """
    try:
        evaluation_service = EvaluationService()
        criteria_info = evaluation_service.get_criteria_info()
        
        return format_success_response(
            data=criteria_info,
            message="Evaluation criteria retrieved successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Error getting criteria: {str(e)}")
        return format_error_response(
            error="Failed to retrieve evaluation criteria",
            code="CRITERIA_ERROR"
        ), 500

@evaluation_bp.route('/evaluate', methods=['POST'])
def evaluate_essay():
    """
    Evaluate essay with comprehensive analysis
    """
    try:
        # Validate request
        if not request.is_json:
            return format_error_response(
                error="Request must be JSO<PERSON>",
                code="INVALID_CONTENT_TYPE"
            ), 400
        
        data = request.get_json()
        
        # Validate input
        validation_result = validate_essay_input(data)
        if not validation_result['valid']:
            return format_error_response(
                error=validation_result['error'],
                code="VALIDATION_ERROR"
            ), 400
        
        essay_text = data.get('essay')
        
        # Perform evaluation
        evaluation_service = EvaluationService()
        result = evaluation_service.evaluate_essay(essay_text)
        
        return format_success_response(
            data=result,
            message="Essay evaluation completed successfully"
        )
        
    except ValueError as e:
        return format_error_response(
            error=str(e),
            code="VALIDATION_ERROR"
        ), 400
        
    except Exception as e:
        current_app.logger.error(f"Evaluation error: {str(e)}")
        return format_error_response(
            error="Internal server error during evaluation",
            code="EVALUATION_ERROR",
            details=str(e) if current_app.debug else None
        ), 500

@evaluation_bp.route('/quick-score', methods=['POST'])
def quick_score():
    """
    Quick scoring without full LLM analysis
    """
    try:
        if not request.is_json:
            return format_error_response(
                error="Request must be JSON",
                code="INVALID_CONTENT_TYPE"
            ), 400
        
        data = request.get_json()
        
        # Validate input
        validation_result = validate_essay_input(data)
        if not validation_result['valid']:
            return format_error_response(
                error=validation_result['error'],
                code="VALIDATION_ERROR"
            ), 400
        
        essay_text = data.get('essay')
        
        # Perform quick evaluation
        evaluation_service = EvaluationService()
        result = evaluation_service.quick_evaluate(essay_text)
        
        return format_success_response(
            data=result,
            message="Quick evaluation completed successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Quick evaluation error: {str(e)}")
        return format_error_response(
            error="Internal server error during quick evaluation",
            code="QUICK_EVALUATION_ERROR"
        ), 500
