#!/usr/bin/env python3
"""
Demo script for Essay Evaluation Service
Shows the complete functionality with sample essays
"""

import requests
import json
import time
from typing import Dict, Any

class EssayEvaluationDemo:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        
    def print_header(self, title):
        print(f"\n{'='*60}")
        print(f"{title.center(60)}")
        print(f"{'='*60}")
    
    def print_section(self, title):
        print(f"\n{'-'*40}")
        print(f"{title}")
        print(f"{'-'*40}")
    
    def check_service_health(self):
        """Check if the service is running"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Service is healthy: {data['service']} v{data['version']}")
                return True
            else:
                print(f"❌ Service health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Cannot connect to service: {e}")
            print(f"   Make sure the service is running at {self.base_url}")
            return False
    
    def evaluate_essay(self, essay: str, title: str) -> Dict[str, Any]:
        """Evaluate an essay and return results"""
        try:
            response = requests.post(f"{self.base_url}/api/v1/evaluate",
                                   json={"essay": essay},
                                   headers={"Content-Type": "application/json"},
                                   timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Evaluation failed for {title}: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ Error evaluating {title}: {e}")
            return None
    
    def display_results(self, results: Dict[str, Any], title: str):
        """Display evaluation results in a formatted way"""
        if not results:
            return
        
        print(f"\n📊 Results for: {title}")
        print(f"Overall Score: {results['overall']}/100")
        print(f"Word Count: {results['word_count']}")
        print(f"LLM Available: {results.get('llm_available', 'Unknown')}")
        
        print("\nCriteria Breakdown:")
        for criterion, details in results['criteria'].items():
            score = details['score']
            feedback = details['feedback']
            print(f"  • {criterion}: {score}/100")
            print(f"    {feedback}")
        
        # Performance rating
        overall = results['overall']
        if overall >= 90:
            rating = "🌟 Exceptional"
        elif overall >= 80:
            rating = "🔥 Excellent"
        elif overall >= 70:
            rating = "👍 Good"
        elif overall >= 60:
            rating = "⚡ Fair"
        else:
            rating = "📝 Needs Improvement"
        
        print(f"\nPerformance Rating: {rating}")
    
    def run_demo(self):
        """Run the complete demonstration"""
        self.print_header("ESSAY EVALUATION SERVICE DEMONSTRATION")
        
        # Check service health
        if not self.check_service_health():
            return
        
        # Sample essays for demonstration
        essays = {
            "Excellent Essay": """
            The moment I first witnessed a neural network successfully classify handwritten digits during my sophomore year, I realized that artificial intelligence was not just a field of study—it was the key to solving humanity's most complex challenges. This epiphany occurred in Professor Kim's machine learning course, where I watched algorithms learn patterns that even humans struggle to recognize.

            My goal is to pursue a Master's degree in Artificial Intelligence at Seoul National University, with a specific focus on developing AI systems for medical diagnosis in underserved communities. Korea's pioneering work in AI healthcare, exemplified by companies like Lunit and Vuno, combined with SNU's world-class research facilities, creates the perfect environment for my research aspirations. Upon graduation, I plan to establish an AI research institute in my home country that bridges Korean technological innovation with local healthcare needs.

            During my undergraduate studies in Computer Science at the National University of Technology, I maintained a cumulative GPA of 3.9/4.0 while conducting research on deep learning applications in medical imaging. My thesis, "Convolutional Neural Networks for Early Detection of Diabetic Retinopathy," was published in the Journal of Medical AI and received the Outstanding Undergraduate Research Award. I also completed advanced coursework in machine learning, computer vision, and biomedical engineering, providing a strong foundation for graduate studies.

            Beyond academics, I have demonstrated leadership as president of the AI Research Society, where I organized international conferences that attracted over 500 participants from 15 countries. I founded a nonprofit organization that provides free AI education to rural students, training over 300 participants in computational thinking and programming. Additionally, I volunteered as a research assistant at the National Medical Center, where I helped develop AI tools for radiologists, directly witnessing the transformative potential of technology in healthcare.

            My vision extends beyond personal achievement to creating lasting impact. I plan to leverage my Korean education to establish collaborative research partnerships between Korean institutions and universities in Southeast Asia, fostering knowledge transfer and technological advancement across borders. Through this work, I aim to democratize access to AI-powered healthcare solutions and contribute to Korea's growing influence as a global leader in artificial intelligence innovation.
            """,
            
            "Good Essay": """
            I have always been fascinated by technology and its potential to solve real-world problems. This interest led me to pursue computer science and develop a passion for artificial intelligence and machine learning.

            My goal is to study at KAIST in Korea to advance my knowledge in AI and machine learning. Korea is known for its technological innovation and has many successful tech companies like Samsung and LG. I believe studying in Korea will provide me with excellent opportunities to learn from leading experts and gain hands-on experience with cutting-edge technology.

            During my undergraduate studies in Computer Science, I maintained a GPA of 3.7/4.0 and completed several projects related to machine learning and data analysis. I wrote my thesis on "Applications of Machine Learning in Data Mining" and presented it at a local conference. I also took courses in algorithms, database systems, and software engineering.

            I have been actively involved in extracurricular activities throughout my academic career. I served as vice president of the Computer Science Student Association and organized programming workshops for fellow students. I also participated in hackathons and coding competitions, winning second place in the university programming contest. Additionally, I volunteered as a tutor for underclassmen struggling with programming concepts.

            After completing my studies in Korea, I plan to return to my home country and work in the technology sector. I hope to contribute to the development of AI solutions that can benefit society and help bridge the technology gap in developing countries.
            """,
            
            "Needs Improvement Essay": """
            I want to study in Korea because I like Korean culture and K-pop. I think Korea is a cool country with advanced technology.

            My goal is to get a degree in computer science. I heard that Korean universities are good and have good programs. I want to learn about computers and maybe work for a Korean company like Samsung.

            I studied computer science in college and got okay grades. I learned programming and took some math classes. I did a project for my final year but it wasn't very complicated.

            I was in some clubs during college and helped with a few events. I also worked part-time at a computer store where I helped customers with their problems.

            After I graduate, I want to get a job in Korea or maybe go back to my country. I think studying in Korea will be a good experience and help me in my career.
            """
        }
        
        # Evaluate each essay
        results = {}
        for title, essay in essays.items():
            self.print_section(f"Evaluating: {title}")
            print(f"Essay length: {len(essay.split())} words")
            
            start_time = time.time()
            result = self.evaluate_essay(essay, title)
            end_time = time.time()
            
            if result:
                results[title] = result
                self.display_results(result, title)
                print(f"⏱️ Evaluation time: {end_time - start_time:.2f} seconds")
            else:
                print(f"❌ Failed to evaluate {title}")
        
        # Summary comparison
        if results:
            self.print_section("COMPARATIVE ANALYSIS")
            print("Essay Performance Summary:")
            for title, result in results.items():
                score = result['overall']
                word_count = result['word_count']
                print(f"  {title}: {score}/100 ({word_count} words)")
            
            # Best performing criteria
            all_criteria = {}
            for result in results.values():
                for criterion, details in result['criteria'].items():
                    if criterion not in all_criteria:
                        all_criteria[criterion] = []
                    all_criteria[criterion].append(details['score'])
            
            print("\nAverage Scores by Criteria:")
            for criterion, scores in all_criteria.items():
                avg_score = sum(scores) / len(scores)
                print(f"  {criterion}: {avg_score:.1f}/100")
        
        # Service information
        self.print_section("SERVICE INFORMATION")
        try:
            response = requests.get(f"{self.base_url}/api/v1/criteria")
            if response.status_code == 200:
                criteria_info = response.json()
                print("Available Evaluation Criteria:")
                for criterion in criteria_info['criteria']:
                    print(f"  • {criterion['name']} ({criterion['weight']})")
                    print(f"    {criterion['description']}")
        except:
            print("Could not retrieve criteria information")
        
        self.print_header("DEMONSTRATION COMPLETE")
        print("🎉 The Essay Evaluation Service is working perfectly!")
        print(f"🌐 Service URL: {self.base_url}")
        print("📚 For more information, see TESTING_README.md")

if __name__ == "__main__":
    demo = EssayEvaluationDemo()
    demo.run_demo()
