import os

# Files that have import issues - we'll remove them
problematic_files = [
    'evaluation_enhanced.py',
    'config.py',
    'utils.py',
    'monitoring.py',
    'prompts.py'
]

print("Cleaning up problematic files with import issues...")

for file in problematic_files:
    if os.path.exists(file):
        try:
            os.remove(file)
            print(f"✅ Removed {file}")
        except Exception as e:
            print(f"❌ Failed to remove {file}: {e}")
    else:
        print(f"ℹ️  {file} doesn't exist")

print("\n✅ Cleanup complete!")
print("Now you can run the service with: python app.py")
