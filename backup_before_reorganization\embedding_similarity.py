#!/usr/bin/env python3
"""
Embedding Similarity Module for Essay Evaluation
Uses Google Gemini API for cost-effective embeddings
"""

import os
import json
import numpy as np
from typing import Dict, List, Any, Optional
import google.generativeai as genai
from dotenv import load_dotenv
from sklearn.metrics.pairwise import cosine_similarity
import pickle
import hashlib

load_dotenv()

class EmbeddingSimilarity:
    """
    Handles embedding generation and similarity scoring using Gemini API
    """

    def __init__(self):
        self.api_key = os.getenv('GEMINI_API_KEY')
        if not self.api_key or self.api_key == 'your_gemini_api_key_here':
            raise ValueError("GEMINI_API_KEY not found or not configured in environment variables")

        # Configure Gemini
        genai.configure(api_key=self.api_key)

        # Cache for embeddings to reduce API calls
        self.cache_file = 'data/embedding_cache.pkl'
        self.embedding_cache = self._load_cache()

        # Reference essays and their embeddings
        self.reference_essays = self._load_reference_essays()
        self.reference_embeddings = None

    def _load_cache(self) -> Dict[str, np.ndarray]:
        """Load embedding cache from disk"""
        try:
            if os.path.exists(self.cache_file):
                with open(self.cache_file, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            print(f"Warning: Could not load embedding cache: {e}")
        return {}

    def _save_cache(self):
        """Save embedding cache to disk"""
        try:
            os.makedirs(os.path.dirname(self.cache_file), exist_ok=True)
            with open(self.cache_file, 'wb') as f:
                pickle.dump(self.embedding_cache, f)
        except Exception as e:
            print(f"Warning: Could not save embedding cache: {e}")

    def _load_reference_essays(self) -> Dict[str, List[str]]:
        """Load reference essays from scholarship_winners.json"""
        try:
            # Try scholarship-specific winners first
            with open('data/scholarship_winners.json', 'r') as f:
                winners = json.load(f)

            # Group by criterion
            reference_essays = {}
            for winner in winners:
                criterion = winner['criterion']
                if criterion not in reference_essays:
                    reference_essays[criterion] = []
                reference_essays[criterion].append(winner['raw_text'])

            return reference_essays
        except FileNotFoundError:
            try:
                # Fallback to original winners.json
                with open('data/winners.json', 'r') as f:
                    winners = json.load(f)

                reference_essays = {}
                for winner in winners:
                    criterion = winner['criterion']
                    if criterion not in reference_essays:
                        reference_essays[criterion] = []
                    reference_essays[criterion].append(winner['raw_text'])

                return reference_essays
            except FileNotFoundError:
                print("Warning: No reference essays found. Using default reference essays.")
                return self._get_default_reference_essays()

    def _get_default_reference_essays(self) -> Dict[str, List[str]]:
        """Default high-quality reference essays for each criterion"""
        return {
            "Introduction/Hook": [
                "The first time I witnessed a child's face light up when they finally grasped a mathematical concept I had been teaching, I knew that education was not just my career choice—it was my calling. This moment, in a small tutoring center in rural Vietnam, crystallized my understanding that learning transcends language barriers and cultural differences.",
                "Standing in the ruins of my earthquake-devastated hometown, I made a promise to myself: I would dedicate my life to developing technologies that could predict and mitigate natural disasters. This defining moment shaped my unwavering commitment to pursuing advanced studies in seismological engineering.",
                "The gentle hum of servers in my university's data center became my sanctuary, where I spent countless nights unraveling the mysteries of machine learning algorithms. It was here, surrounded by the digital heartbeat of innovation, that I discovered my passion for artificial intelligence."
            ],
            "Motivation": [
                "My goal is to pursue a Master's in Artificial Intelligence at KAIST, specifically focusing on natural language processing for Southeast Asian languages. Korea's leadership in AI research, combined with companies like Samsung and LG pioneering human-computer interaction, makes it the ideal environment for my research. Upon completion, I plan to return to Thailand to establish an AI research lab that bridges Korean technological innovation with Southeast Asian linguistic diversity.",
                "I aspire to study renewable energy engineering at Seoul National University, driven by Korea's remarkable transition to sustainable energy systems. The country's innovative approach to integrating solar and wind technologies with smart grid systems aligns perfectly with my vision of creating carbon-neutral communities in developing nations.",
                "My objective is to pursue advanced studies in biotechnology at Yonsei University, leveraging Korea's cutting-edge research in personalized medicine and genetic therapies. I aim to develop affordable diagnostic tools for rare diseases, combining Korean innovation with global health equity."
            ],
            "Educational Background": [
                "During my undergraduate studies in Electrical Engineering at the University of Indonesia, I maintained a GPA of 3.85/4.0 while conducting research on renewable energy systems. My thesis on 'Solar Panel Efficiency Optimization Using Machine Learning' was published in the IEEE Indonesian Conference and earned the Best Undergraduate Research Award. Additionally, I completed advanced coursework in signal processing and control systems, which directly aligns with my graduate study goals.",
                "Throughout my Computer Science degree at the National University of Technology, I achieved a cumulative GPA of 3.9/4.0 while specializing in artificial intelligence and machine learning. My research on neural network optimization resulted in two peer-reviewed publications and a patent application for an innovative deep learning architecture.",
                "My academic journey in Biomedical Engineering at the University of Science has been marked by consistent excellence, maintaining a 3.8/4.0 GPA while conducting groundbreaking research on medical device innovation. My senior thesis on smart prosthetics won the national undergraduate research competition and has been cited in multiple academic journals."
            ],
            "Extracurricular": [
                "As president of the University Environmental Club, I led a team of 50 students in organizing the 'Green Campus Initiative,' which reduced university waste by 40% and installed solar panels across three dormitories. Additionally, I volunteered weekly at a local orphanage, teaching English to children ages 8-12, and represented my university at the International Student Climate Action Conference in Singapore, where our proposal for sustainable campus transportation won first place.",
                "During my tenure as founder and president of the AI Research Society, I organized international conferences that attracted over 500 participants from 15 countries. I established a mentorship program connecting undergraduates with industry professionals, launched a coding bootcamp for underprivileged youth that trained over 200 students, and led our team to victory in three national hackathons.",
                "As captain of the university debate team, I led our squad to win the National Collegiate Debate Championship for two consecutive years. Simultaneously, I founded a nonprofit organization providing free STEM education to rural communities, personally teaching over 300 students and establishing learning centers in five villages."
            ]
        }

    def _get_text_hash(self, text: str) -> str:
        """Generate a hash for text to use as cache key"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    def get_embedding(self, text: str) -> np.ndarray:
        """
        Get embedding for text using Gemini API with caching
        """
        # Check cache first
        text_hash = self._get_text_hash(text)
        if text_hash in self.embedding_cache:
            return self.embedding_cache[text_hash]

        try:
            # Use Gemini embedding model
            result = genai.embed_content(
                model="models/embedding-001",
                content=text,
                task_type="semantic_similarity"
            )

            embedding = np.array(result['embedding'])

            # Cache the result
            self.embedding_cache[text_hash] = embedding
            self._save_cache()

            return embedding

        except Exception as e:
            raise Exception(f"Failed to get embedding from Gemini API: {str(e)}")

    def _ensure_reference_embeddings(self):
        """Ensure reference embeddings are computed and cached"""
        if self.reference_embeddings is not None:
            return

        print("Computing reference embeddings...")
        self.reference_embeddings = {}

        for criterion, essays in self.reference_essays.items():
            embeddings = []
            for essay in essays:
                try:
                    embedding = self.get_embedding(essay)
                    embeddings.append(embedding)
                except Exception as e:
                    print(f"Warning: Failed to get embedding for {criterion}: {e}")

            if embeddings:
                self.reference_embeddings[criterion] = np.array(embeddings)

        print("Reference embeddings computed successfully")

    def compute_similarity_scores(self, essay_text: str) -> Dict[str, Any]:
        """
        Compute similarity scores for an essay against reference essays
        """
        self._ensure_reference_embeddings()

        try:
            # Get embedding for the input essay
            essay_embedding = self.get_embedding(essay_text)

            similarity_scores = {}
            detailed_scores = {}

            for criterion, ref_embeddings in self.reference_embeddings.items():
                if len(ref_embeddings) == 0:
                    continue

                # Compute cosine similarities with all reference essays for this criterion
                similarities = cosine_similarity([essay_embedding], ref_embeddings)[0]

                # Statistics
                max_similarity = float(np.max(similarities))
                mean_similarity = float(np.mean(similarities))
                std_similarity = float(np.std(similarities))

                # Convert similarity to score (0-100)
                # Use a sigmoid-like transformation to map similarity to score
                base_score = max_similarity * 100

                # Adjust based on consistency (lower std means more consistent quality)
                consistency_bonus = max(0, (0.1 - std_similarity) * 50)

                # Final score with some normalization
                final_score = min(100, max(0, base_score + consistency_bonus))

                similarity_scores[criterion] = {
                    "score": round(final_score, 1),
                    "max_similarity": round(max_similarity, 3),
                    "mean_similarity": round(mean_similarity, 3),
                    "std_similarity": round(std_similarity, 3),
                    "num_references": len(ref_embeddings)
                }

                # Generate feedback based on similarity
                if max_similarity >= 0.85:
                    feedback = "Excellent semantic similarity to high-quality reference essays"
                elif max_similarity >= 0.75:
                    feedback = "Good semantic alignment with reference standards"
                elif max_similarity >= 0.65:
                    feedback = "Moderate similarity to reference essays, room for improvement"
                elif max_similarity >= 0.55:
                    feedback = "Below average similarity to high-quality examples"
                else:
                    feedback = "Low semantic similarity to reference standards"

                detailed_scores[criterion] = {
                    "score": round(final_score, 1),
                    "feedback": feedback
                }

            return {
                "similarity_scores": detailed_scores,
                "similarity_details": similarity_scores,
                "embedding_available": True
            }

        except Exception as e:
            return {
                "similarity_scores": {},
                "similarity_details": {},
                "embedding_available": False,
                "error": str(e)
            }

    def extract_essay_sections(self, essay_text: str) -> Dict[str, str]:
        """
        Extract different sections of an essay for criterion-specific analysis
        This is a simple heuristic-based approach
        """
        paragraphs = [p.strip() for p in essay_text.split('\n\n') if p.strip()]

        if len(paragraphs) == 0:
            return {}

        sections = {}

        # First paragraph is usually introduction
        if len(paragraphs) >= 1:
            sections["Introduction/Hook"] = paragraphs[0]

        # Try to identify motivation/goals (look for keywords)
        motivation_keywords = ['goal', 'objective', 'aim', 'plan', 'aspire', 'vision', 'pursue']
        for para in paragraphs:
            if any(keyword in para.lower() for keyword in motivation_keywords):
                sections["Motivation"] = para
                break

        # Educational background (look for academic keywords)
        education_keywords = ['university', 'college', 'degree', 'gpa', 'thesis', 'research', 'study', 'academic']
        for para in paragraphs:
            if any(keyword in para.lower() for keyword in education_keywords):
                sections["Educational Background"] = para
                break

        # Extracurricular (look for activity keywords)
        activity_keywords = ['president', 'leader', 'volunteer', 'club', 'organization', 'team', 'competition']
        for para in paragraphs:
            if any(keyword in para.lower() for keyword in activity_keywords):
                sections["Extracurricular"] = para
                break

        # If we couldn't identify specific sections, use the whole essay for each criterion
        criteria = ["Introduction/Hook", "Motivation", "Educational Background", "Extracurricular"]
        for criterion in criteria:
            if criterion not in sections:
                sections[criterion] = essay_text

        return sections

    def evaluate_with_similarity(self, essay_text: str) -> Dict[str, Any]:
        """
        Evaluate essay using embedding similarity with section-specific analysis
        """
        try:
            # Extract sections
            sections = self.extract_essay_sections(essay_text)

            criterion_scores = {}
            overall_similarity_details = {}

            for criterion in ["Introduction/Hook", "Motivation", "Educational Background", "Extracurricular"]:
                section_text = sections.get(criterion, essay_text)

                # Get similarity scores for this section
                similarity_result = self.compute_similarity_scores(section_text)

                if similarity_result["embedding_available"] and criterion in similarity_result["similarity_scores"]:
                    score_data = similarity_result["similarity_scores"][criterion]
                    criterion_scores[criterion] = score_data
                    overall_similarity_details[criterion] = similarity_result["similarity_details"][criterion]
                else:
                    # Fallback scoring
                    criterion_scores[criterion] = {
                        "score": 50.0,
                        "feedback": "Embedding similarity not available"
                    }

            # Calculate overall similarity score
            if criterion_scores:
                weights = {
                    "Introduction/Hook": 0.25,
                    "Motivation": 0.30,
                    "Educational Background": 0.25,
                    "Extracurricular": 0.20
                }

                overall_score = sum(
                    criterion_scores[criterion]["score"] * weights.get(criterion, 0.25)
                    for criterion in criterion_scores
                )
            else:
                overall_score = 50.0

            return {
                "overall_similarity_score": round(overall_score, 1),
                "criterion_scores": criterion_scores,
                "similarity_details": overall_similarity_details,
                "sections_analyzed": len(sections),
                "embedding_available": True
            }

        except Exception as e:
            return {
                "overall_similarity_score": 0.0,
                "criterion_scores": {},
                "similarity_details": {},
                "sections_analyzed": 0,
                "embedding_available": False,
                "error": str(e)
            }

# Global instance
embedding_similarity = None

def get_embedding_similarity() -> EmbeddingSimilarity:
    """Get or create global embedding similarity instance"""
    global embedding_similarity
    if embedding_similarity is None:
        try:
            embedding_similarity = EmbeddingSimilarity()
        except Exception as e:
            print(f"Warning: Could not initialize embedding similarity: {e}")
            return None
    return embedding_similarity

def compute_embedding_similarity(essay_text: str) -> Dict[str, Any]:
    """
    Compute embedding similarity scores for an essay
    """
    similarity_engine = get_embedding_similarity()
    if similarity_engine is None:
        return {
            "overall_similarity_score": 0.0,
            "criterion_scores": {},
            "embedding_available": False,
            "error": "Embedding similarity not available"
        }

    return similarity_engine.evaluate_with_similarity(essay_text)
