#!/usr/bin/env python3
"""
Test script for embedding similarity functionality
Tests the Gemini-based embedding similarity system
"""

import os
import sys
import json
import requests
from dotenv import load_dotenv

load_dotenv()

def test_gemini_api_key():
    """Test if Gemini API key is configured"""
    print("🔑 Testing Gemini API Configuration")
    print("-" * 40)
    
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ GEMINI_API_KEY not found in .env file")
        print("   Please add: GEMINI_API_KEY=your_gemini_api_key_here")
        return False
    elif api_key == 'your_gemini_api_key_here':
        print("❌ GEMINI_API_KEY is still the placeholder value")
        print("   Please get your API key from: https://makersuite.google.com/app/apikey")
        return False
    else:
        print(f"✅ GEMINI_API_KEY is configured (key: {api_key[:10]}...)")
        return True

def test_dependencies():
    """Test if required dependencies are installed"""
    print("\n📦 Testing Dependencies")
    print("-" * 40)
    
    required_packages = {
        'numpy': 'numpy',
        'google.generativeai': 'google-generativeai',
        'sklearn': 'scikit-learn'
    }
    
    all_available = True
    
    for import_name, package_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"✅ {package_name} is installed")
        except ImportError:
            print(f"❌ {package_name} is not installed")
            print(f"   Install with: pip install {package_name}")
            all_available = False
    
    return all_available

def test_embedding_module():
    """Test if the embedding similarity module works"""
    print("\n🧪 Testing Embedding Module")
    print("-" * 40)
    
    try:
        from embedding_similarity import EmbeddingSimilarity, compute_embedding_similarity
        print("✅ Embedding similarity module imported successfully")
        
        # Test basic functionality
        test_text = "I want to study computer science in Korea because of its advanced technology."
        
        try:
            result = compute_embedding_similarity(test_text)
            if result.get("embedding_available", False):
                print("✅ Embedding similarity computation works")
                print(f"   Overall similarity score: {result.get('overall_similarity_score', 0)}")
                return True
            else:
                print("❌ Embedding similarity not available")
                if "error" in result:
                    print(f"   Error: {result['error']}")
                return False
        except Exception as e:
            print(f"❌ Error computing embedding similarity: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ Cannot import embedding similarity module: {e}")
        return False

def test_api_endpoint():
    """Test the similarity API endpoint"""
    print("\n🌐 Testing API Endpoint")
    print("-" * 40)
    
    base_url = "http://localhost:5000"
    
    # Check if service is running
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code != 200:
            print("❌ Service is not running")
            print("   Start the service with: python app.py")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to service")
        print("   Start the service with: python app.py")
        return False
    
    print("✅ Service is running")
    
    # Test similarity endpoint
    test_essay = """
    I am passionate about studying artificial intelligence in Korea because of its advanced technology sector.
    My goal is to pursue a Master's degree at KAIST to research machine learning applications.
    
    During my undergraduate studies in Computer Science, I maintained a 3.8 GPA and published research papers.
    I have experience in programming, data analysis, and software development.
    
    As president of the coding club, I organized hackathons and mentored junior students.
    I also volunteered at local schools teaching programming to children.
    
    After graduation, I plan to return to my home country and establish a tech startup
    that leverages Korean AI innovations to solve local problems.
    """
    
    try:
        response = requests.post(f"{base_url}/api/v1/similarity",
                               json={"essay": test_essay},
                               headers={"Content-Type": "application/json"},
                               timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            similarity_analysis = data.get("similarity_analysis", {})
            
            print("✅ Similarity API endpoint works")
            print(f"   Overall similarity score: {similarity_analysis.get('overall_similarity_score', 0)}")
            print(f"   Embedding available: {similarity_analysis.get('embedding_available', False)}")
            
            if similarity_analysis.get("criterion_scores"):
                print("   Criterion scores:")
                for criterion, score_data in similarity_analysis["criterion_scores"].items():
                    print(f"     • {criterion}: {score_data['score']}")
            
            return True
        else:
            print(f"❌ Similarity API failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing similarity API: {e}")
        return False

def test_full_evaluation_with_embedding():
    """Test the full evaluation with embedding similarity integrated"""
    print("\n🎯 Testing Full Evaluation with Embedding")
    print("-" * 40)
    
    base_url = "http://localhost:5000"
    
    test_essay = """
    The first time I witnessed a neural network successfully classify handwritten digits during my sophomore year, 
    I realized that artificial intelligence was not just a field of study—it was the key to solving humanity's 
    most complex challenges. This epiphany occurred in Professor Kim's machine learning course, where I watched 
    algorithms learn patterns that even humans struggle to recognize.

    My goal is to pursue a Master's degree in Artificial Intelligence at Seoul National University, with a 
    specific focus on developing AI systems for medical diagnosis in underserved communities. Korea's pioneering 
    work in AI healthcare, exemplified by companies like Lunit and Vuno, combined with SNU's world-class research 
    facilities, creates the perfect environment for my research aspirations.

    During my undergraduate studies in Computer Science at the National University of Technology, I maintained 
    a cumulative GPA of 3.9/4.0 while conducting research on deep learning applications in medical imaging. 
    My thesis, "Convolutional Neural Networks for Early Detection of Diabetic Retinopathy," was published in 
    the Journal of Medical AI and received the Outstanding Undergraduate Research Award.

    Beyond academics, I have demonstrated leadership as president of the AI Research Society, where I organized 
    international conferences that attracted over 500 participants from 15 countries. I founded a nonprofit 
    organization that provides free AI education to rural students, training over 300 participants in 
    computational thinking and programming.
    """
    
    try:
        response = requests.post(f"{base_url}/api/v1/evaluate",
                               json={"essay": test_essay},
                               headers={"Content-Type": "application/json"},
                               timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ Full evaluation with embedding works")
            print(f"   Overall score: {data.get('overall', 0)}/100")
            print(f"   LLM available: {data.get('llm_available', False)}")
            print(f"   Embedding available: {data.get('embedding_available', False)}")
            
            embedding_similarity = data.get('embedding_similarity', {})
            print(f"   Embedding similarity score: {embedding_similarity.get('overall_score', 0)}")
            
            print("\n   Criteria scores:")
            for criterion, details in data.get('criteria', {}).items():
                print(f"     • {criterion}: {details['score']}/100")
            
            return True
        else:
            print(f"❌ Full evaluation failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing full evaluation: {e}")
        return False

def main():
    """Run all embedding similarity tests"""
    print("🚀 Embedding Similarity Test Suite")
    print("=" * 50)
    
    tests = [
        ("API Key Configuration", test_gemini_api_key),
        ("Dependencies", test_dependencies),
        ("Embedding Module", test_embedding_module),
        ("API Endpoint", test_api_endpoint),
        ("Full Evaluation", test_full_evaluation_with_embedding)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"\n❌ {test_name} test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All embedding similarity tests passed!")
        print("   The embedding similarity system is ready to use.")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed.")
        print("   Please fix the issues above before using embedding similarity.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
