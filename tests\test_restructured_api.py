#!/usr/bin/env python3
"""
Test script for the restructured API
Tests all endpoints and functionality
"""

import requests
import json
import time
from typing import Dict, Any

class RestructuredAPITester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        
    def print_header(self, title):
        print(f"\n{'='*70}")
        print(f"{title.center(70)}")
        print(f"{'='*70}")
    
    def print_section(self, title):
        print(f"\n{'-'*50}")
        print(f"{title}")
        print(f"{'-'*50}")
    
    def test_health_endpoints(self):
        """Test health and status endpoints"""
        self.print_section("Testing Health Endpoints")
        
        # Test basic health
        try:
            response = requests.get(f"{self.base_url}/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Health check: {data.get('status', 'unknown')}")
                print(f"   Service: {data.get('service', 'unknown')}")
                print(f"   Version: {data.get('version', 'unknown')}")
            else:
                print(f"❌ Health check failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Health check error: {e}")
        
        # Test detailed status
        try:
            response = requests.get(f"{self.base_url}/status")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Status check: {data.get('status', 'unknown')}")
                components = data.get('components', {})
                for component, status in components.items():
                    print(f"   {component}: {status}")
            else:
                print(f"❌ Status check failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Status check error: {e}")
    
    def test_evaluation_endpoints(self):
        """Test evaluation endpoints"""
        self.print_section("Testing Evaluation Endpoints")
        
        sample_essay = """
        I am passionate about studying artificial intelligence in Korea because of its advanced technology sector.
        My goal is to pursue a Master's degree at KAIST to research machine learning applications.
        
        During my undergraduate studies in Computer Science, I maintained a 3.8 GPA and published research papers.
        I have experience in programming, data analysis, and software development.
        
        As president of the coding club, I organized hackathons and mentored junior students.
        I also volunteered at local schools teaching programming to children.
        
        After graduation, I plan to return to my home country and establish a tech startup
        that leverages Korean AI innovations to solve local problems.
        """
        
        # Test criteria endpoint
        try:
            response = requests.get(f"{self.base_url}/api/v1/evaluation/criteria")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Criteria endpoint: {data.get('success', False)}")
                criteria = data.get('data', {}).get('criteria', [])
                print(f"   Found {len(criteria)} evaluation criteria")
            else:
                print(f"❌ Criteria endpoint failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Criteria endpoint error: {e}")
        
        # Test comprehensive evaluation
        try:
            response = requests.post(f"{self.base_url}/api/v1/evaluation/evaluate",
                                   json={"essay": sample_essay})
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Comprehensive evaluation: {data.get('success', False)}")
                result = data.get('data', {})
                print(f"   Overall score: {result.get('overall', 0)}/100")
                print(f"   Word count: {result.get('word_count', 0)}")
            else:
                print(f"❌ Comprehensive evaluation failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Comprehensive evaluation error: {e}")
        
        # Test quick scoring
        try:
            response = requests.post(f"{self.base_url}/api/v1/evaluation/quick-score",
                                   json={"essay": sample_essay})
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Quick scoring: {data.get('success', False)}")
                result = data.get('data', {})
                print(f"   Quick score: {result.get('overall', 0)}/100")
            else:
                print(f"❌ Quick scoring failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Quick scoring error: {e}")
    
    def test_scholarship_endpoints(self):
        """Test scholarship endpoints"""
        self.print_section("Testing Scholarship Endpoints")
        
        # Test scholarship list
        try:
            response = requests.get(f"{self.base_url}/api/v1/scholarship/list")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Scholarship list: {data.get('success', False)}")
                scholarships = data.get('data', {}).get('scholarships', [])
                print(f"   Found {len(scholarships)} scholarships")
                for scholarship in scholarships[:3]:
                    print(f"   {scholarship.get('logo', '')} {scholarship.get('name', '')}")
                return scholarships
            else:
                print(f"❌ Scholarship list failed: {response.status_code}")
                return []
        except Exception as e:
            print(f"❌ Scholarship list error: {e}")
            return []
    
    def test_analysis_endpoints(self):
        """Test analysis endpoints"""
        self.print_section("Testing Analysis Endpoints")
        
        sample_essay = "This is a test essay for analysis. It contains multiple sentences and paragraphs."
        
        # Test structure analysis
        try:
            response = requests.post(f"{self.base_url}/api/v1/analysis/structure",
                                   json={"essay": sample_essay})
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Structure analysis: {data.get('success', False)}")
            else:
                print(f"❌ Structure analysis failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Structure analysis error: {e}")
        
        # Test readability analysis
        try:
            response = requests.post(f"{self.base_url}/api/v1/analysis/readability",
                                   json={"essay": sample_essay})
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Readability analysis: {data.get('success', False)}")
            else:
                print(f"❌ Readability analysis failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Readability analysis error: {e}")
        
        # Test comprehensive analysis
        try:
            response = requests.post(f"{self.base_url}/api/v1/analysis/comprehensive",
                                   json={"essay": sample_essay})
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Comprehensive analysis: {data.get('success', False)}")
            else:
                print(f"❌ Comprehensive analysis failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Comprehensive analysis error: {e}")
    
    def test_similarity_endpoints(self):
        """Test similarity endpoints"""
        self.print_section("Testing Similarity Endpoints")
        
        sample_essay = "I want to study artificial intelligence in Korea to advance my research."
        
        # Test similarity status
        try:
            response = requests.get(f"{self.base_url}/api/v1/similarity/status")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Similarity status: {data.get('success', False)}")
                status_data = data.get('data', {})
                print(f"   Embedding available: {status_data.get('embedding_available', False)}")
            else:
                print(f"❌ Similarity status failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Similarity status error: {e}")
        
        # Test similarity analysis
        try:
            response = requests.post(f"{self.base_url}/api/v1/similarity/analyze",
                                   json={"essay": sample_essay})
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Similarity analysis: {data.get('success', False)}")
            else:
                print(f"❌ Similarity analysis failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Similarity analysis error: {e}")
    
    def test_error_handling(self):
        """Test error handling"""
        self.print_section("Testing Error Handling")
        
        # Test 404 error
        try:
            response = requests.get(f"{self.base_url}/api/v1/nonexistent")
            if response.status_code == 404:
                print("✅ 404 error handling works")
            else:
                print(f"❌ 404 error handling failed: {response.status_code}")
        except Exception as e:
            print(f"❌ 404 error test error: {e}")
        
        # Test validation error
        try:
            response = requests.post(f"{self.base_url}/api/v1/evaluation/evaluate",
                                   json={"essay": ""})
            if response.status_code == 400:
                print("✅ Validation error handling works")
            else:
                print(f"❌ Validation error handling failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Validation error test error: {e}")
    
    def test_api_info(self):
        """Test API info endpoint"""
        self.print_section("Testing API Info")
        
        try:
            response = requests.get(f"{self.base_url}/api/info")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API info: {data.get('success', False)}")
                api_info = data.get('api', {})
                print(f"   Title: {api_info.get('title', 'unknown')}")
                print(f"   Version: {api_info.get('version', 'unknown')}")
                features = data.get('features', {})
                print("   Features:")
                for feature, enabled in features.items():
                    status = "✅" if enabled else "❌"
                    print(f"     {status} {feature}")
            else:
                print(f"❌ API info failed: {response.status_code}")
        except Exception as e:
            print(f"❌ API info error: {e}")
    
    def run_comprehensive_test(self):
        """Run all tests"""
        self.print_header("RESTRUCTURED API COMPREHENSIVE TEST")
        
        print("🧪 Testing the new Flask Blueprint architecture")
        print("📊 Checking all endpoints and functionality")
        
        # Run all test categories
        self.test_health_endpoints()
        self.test_api_info()
        self.test_evaluation_endpoints()
        self.test_scholarship_endpoints()
        self.test_analysis_endpoints()
        self.test_similarity_endpoints()
        self.test_error_handling()
        
        # Summary
        self.print_section("TEST SUMMARY")
        print("🎉 Restructured API testing completed!")
        print("\n📋 New Architecture Features:")
        print("  ✅ Flask Blueprints for modular organization")
        print("  ✅ RESTful API design with proper HTTP methods")
        print("  ✅ Consistent response formatting")
        print("  ✅ Comprehensive error handling")
        print("  ✅ Service-oriented architecture")
        print("  ✅ Configuration management")
        print("  ✅ Input validation utilities")
        print("  ✅ Organized folder structure")
        
        print(f"\n🌐 API Base URL: {self.base_url}")
        print("📚 Ready for production deployment!")

if __name__ == "__main__":
    tester = RestructuredAPITester()
    tester.run_comprehensive_test()
