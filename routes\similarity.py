"""
Embedding similarity analysis routes
"""

from flask import Blueprint, request, jsonify, current_app
from services.similarity_service import SimilarityService
from utils.validators import validate_essay_input
from utils.response_formatter import format_success_response, format_error_response

similarity_bp = Blueprint('similarity', __name__)

@similarity_bp.route('/analyze', methods=['POST'])
def analyze_similarity():
    """
    Analyze essay similarity to reference essays using embeddings
    """
    try:
        if not request.is_json:
            return format_error_response(
                error="Request must be JSON",
                code="INVALID_CONTENT_TYPE"
            ), 400
        
        data = request.get_json()
        
        # Validate input
        validation_result = validate_essay_input(data)
        if not validation_result['valid']:
            return format_error_response(
                error=validation_result['error'],
                code="VALIDATION_ERROR"
            ), 400
        
        essay_text = data.get('essay')
        
        # Optional parameters
        scholarship_type = data.get('scholarship_type')
        essay_type = data.get('essay_type')
        
        # Perform similarity analysis
        similarity_service = SimilarityService()
        
        if not similarity_service.is_available():
            return format_error_response(
                error="Embedding similarity not available",
                code="EMBEDDING_NOT_AVAILABLE",
                message="Install required dependencies: numpy, google-generativeai, scikit-learn"
            ), 503
        
        result = similarity_service.analyze_similarity(
            essay_text, 
            scholarship_type=scholarship_type,
            essay_type=essay_type
        )
        
        return format_success_response(
            data={
                "similarity_analysis": result,
                "analysis_type": "embedding_similarity"
            },
            message="Similarity analysis completed successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Similarity analysis error: {str(e)}")
        return format_error_response(
            error="Internal server error during similarity analysis",
            code="SIMILARITY_ANALYSIS_ERROR",
            details=str(e) if current_app.debug else None
        ), 500

@similarity_bp.route('/compare-references', methods=['POST'])
def compare_with_references():
    """
    Compare essay with specific reference essays
    """
    try:
        if not request.is_json:
            return format_error_response(
                error="Request must be JSON",
                code="INVALID_CONTENT_TYPE"
            ), 400
        
        data = request.get_json()
        
        essay_text = data.get('essay', '').strip()
        reference_essays = data.get('reference_essays', [])
        
        if not essay_text:
            return format_error_response(
                error="Essay text cannot be empty",
                code="EMPTY_ESSAY"
            ), 400
        
        if not reference_essays:
            return format_error_response(
                error="Reference essays are required",
                code="NO_REFERENCES"
            ), 400
        
        # Perform comparison
        similarity_service = SimilarityService()
        
        if not similarity_service.is_available():
            return format_error_response(
                error="Embedding similarity not available",
                code="EMBEDDING_NOT_AVAILABLE"
            ), 503
        
        result = similarity_service.compare_with_references(essay_text, reference_essays)
        
        return format_success_response(
            data=result,
            message="Reference comparison completed successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Reference comparison error: {str(e)}")
        return format_error_response(
            error="Internal server error during reference comparison",
            code="REFERENCE_COMPARISON_ERROR"
        ), 500

@similarity_bp.route('/status', methods=['GET'])
def similarity_status():
    """
    Get embedding similarity service status
    """
    try:
        similarity_service = SimilarityService()
        
        status = {
            "embedding_available": similarity_service.is_available(),
            "provider": "Google Gemini" if similarity_service.is_available() else None,
            "cache_status": similarity_service.get_cache_status() if similarity_service.is_available() else None,
            "reference_essays_count": similarity_service.get_reference_count() if similarity_service.is_available() else 0
        }
        
        return format_success_response(
            data=status,
            message="Similarity service status retrieved successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Similarity status error: {str(e)}")
        return format_error_response(
            error="Failed to get similarity service status",
            code="SIMILARITY_STATUS_ERROR"
        ), 500

@similarity_bp.route('/cache/clear', methods=['POST'])
def clear_cache():
    """
    Clear embedding cache
    """
    try:
        similarity_service = SimilarityService()
        
        if not similarity_service.is_available():
            return format_error_response(
                error="Embedding similarity not available",
                code="EMBEDDING_NOT_AVAILABLE"
            ), 503
        
        cleared_count = similarity_service.clear_cache()
        
        return format_success_response(
            data={"cleared_embeddings": cleared_count},
            message="Embedding cache cleared successfully"
        )
        
    except Exception as e:
        current_app.logger.error(f"Cache clear error: {str(e)}")
        return format_error_response(
            error="Failed to clear embedding cache",
            code="CACHE_CLEAR_ERROR"
        ), 500
