"""
Essay evaluation service
Handles core essay evaluation logic
"""

import os
import sys
from typing import Dict, Any

# Add the parent directory to the path to import legacy modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class EvaluationService:
    """
    Service for essay evaluation with LLM and embedding similarity
    """
    
    def __init__(self):
        self.llm_available = self._check_llm_availability()
        self.embedding_available = self._check_embedding_availability()
    
    def _check_llm_availability(self) -> bool:
        """Check if LLM evaluation is available"""
        try:
            from dotenv import load_dotenv
            load_dotenv()
            
            api_key = os.getenv('OPENROUTER_API_KEY')
            return api_key is not None and api_key != 'your_openrouter_api_key_here'
        except:
            return False
    
    def _check_embedding_availability(self) -> bool:
        """Check if embedding similarity is available"""
        try:
            import google.generativeai as genai
            import numpy as np
            from sklearn.metrics.pairwise import cosine_similarity
            
            from dotenv import load_dotenv
            load_dotenv()
            
            api_key = os.getenv('GEMINI_API_KEY')
            return api_key is not None and api_key != 'your_gemini_api_key_here'
        except ImportError:
            return False
        except:
            return False
    
    def get_criteria_info(self) -> Dict[str, Any]:
        """Get evaluation criteria information"""
        return {
            "criteria": [
                {
                    "name": "Format Compliance",
                    "weight": 20,
                    "description": "Essay structure, word count, and formatting"
                },
                {
                    "name": "Introduction/Hook",
                    "weight": 20,
                    "description": "Opening quality and engagement"
                },
                {
                    "name": "Motivation",
                    "weight": 25,
                    "description": "Goals, aspirations, and reasons for application"
                },
                {
                    "name": "Educational Background",
                    "weight": 20,
                    "description": "Academic achievements and qualifications"
                },
                {
                    "name": "Extracurricular",
                    "weight": 15,
                    "description": "Leadership, community involvement, and activities"
                }
            ],
            "scoring_scale": {
                "excellent": {"min": 80, "max": 100, "description": "Outstanding quality"},
                "good": {"min": 60, "max": 79, "description": "Above average quality"},
                "fair": {"min": 40, "max": 59, "description": "Average quality"},
                "poor": {"min": 0, "max": 39, "description": "Below average quality"}
            },
            "features": {
                "llm_evaluation": self.llm_available,
                "embedding_similarity": self.embedding_available,
                "fast_rules": True,
                "detailed_feedback": True
            }
        }
    
    def evaluate_essay(self, essay_text: str) -> Dict[str, Any]:
        """
        Perform comprehensive essay evaluation
        """
        if not essay_text or not essay_text.strip():
            raise ValueError("Essay text cannot be empty")
        
        try:
            # Import the legacy evaluation function
            from evaluation import evaluate
            
            result = evaluate(essay_text)
            
            # Add service metadata
            result['service_info'] = {
                'llm_available': self.llm_available,
                'embedding_available': self.embedding_available,
                'evaluation_type': 'comprehensive'
            }
            
            return result
            
        except Exception as e:
            # Fallback to basic evaluation if main evaluation fails
            return self._fallback_evaluation(essay_text, str(e))
    
    def quick_evaluate(self, essay_text: str) -> Dict[str, Any]:
        """
        Perform quick evaluation using only fast rules
        """
        if not essay_text or not essay_text.strip():
            raise ValueError("Essay text cannot be empty")
        
        try:
            # Import the legacy fast rules function
            from evaluation import enhanced_fast_rules
            
            result = enhanced_fast_rules(essay_text)
            
            # Format for consistency with full evaluation
            formatted_result = {
                "overall": result["score"],
                "criteria": {
                    "Format Compliance": {
                        "score": result["score"],
                        "feedback": result["feedback"]
                    }
                },
                "word_count": len(essay_text.split()),
                "evaluation_timestamp": result.get("timestamp"),
                "analysis_details": result.get("details", {}),
                "service_info": {
                    'llm_available': False,
                    'embedding_available': False,
                    'evaluation_type': 'quick'
                }
            }
            
            return formatted_result
            
        except Exception as e:
            return self._fallback_evaluation(essay_text, str(e))
    
    def _fallback_evaluation(self, essay_text: str, error_message: str) -> Dict[str, Any]:
        """
        Fallback evaluation when main evaluation fails
        """
        word_count = len(essay_text.split())
        
        # Basic scoring based on word count and length
        if word_count < 50:
            score = 30
            feedback = "Essay is too short. Please provide more detailed content."
        elif word_count > 1500:
            score = 60
            feedback = "Essay is quite long. Consider being more concise."
        elif 200 <= word_count <= 800:
            score = 70
            feedback = "Essay length is appropriate. Content analysis unavailable."
        else:
            score = 50
            feedback = "Basic analysis only. Full evaluation service unavailable."
        
        return {
            "overall": score,
            "criteria": {
                "Format Compliance": {
                    "score": score,
                    "feedback": feedback
                }
            },
            "word_count": word_count,
            "evaluation_timestamp": None,
            "analysis_details": {
                "fallback_mode": True,
                "error": error_message
            },
            "service_info": {
                'llm_available': False,
                'embedding_available': False,
                'evaluation_type': 'fallback'
            }
        }
