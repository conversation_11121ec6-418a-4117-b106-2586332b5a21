"""
Embedding similarity service
Handles semantic similarity analysis using embeddings
"""

import os
import sys
from typing import Dict, List, Any, Optional

# Add the parent directory to the path to import legacy modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class SimilarityService:
    """
    Service for embedding similarity analysis
    """
    
    def __init__(self):
        self.available = self._check_availability()
        if self.available:
            self._load_similarity_engine()
    
    def _check_availability(self) -> bool:
        """Check if embedding similarity is available"""
        try:
            import google.generativeai as genai
            import numpy as np
            from sklearn.metrics.pairwise import cosine_similarity
            
            from dotenv import load_dotenv
            load_dotenv()
            
            api_key = os.getenv('GEMINI_API_KEY')
            return api_key is not None and api_key != 'your_gemini_api_key_here'
        except ImportError:
            return False
        except:
            return False
    
    def _load_similarity_engine(self):
        """Load the embedding similarity engine"""
        try:
            from embedding_similarity import get_embedding_similarity
            self.similarity_engine = get_embedding_similarity()
        except ImportError:
            self.similarity_engine = None
            self.available = False
    
    def is_available(self) -> bool:
        """Check if similarity service is available"""
        return self.available and self.similarity_engine is not None
    
    def analyze_similarity(self, essay_text: str, scholarship_type: Optional[str] = None, 
                          essay_type: Optional[str] = None) -> Dict[str, Any]:
        """
        Analyze essay similarity to reference essays
        """
        if not self.is_available():
            return {
                "overall_similarity_score": 0.0,
                "criterion_scores": {},
                "embedding_available": False,
                "error": "Embedding similarity service not available"
            }
        
        try:
            from embedding_similarity import compute_embedding_similarity
            return compute_embedding_similarity(essay_text)
        except Exception as e:
            return {
                "overall_similarity_score": 0.0,
                "criterion_scores": {},
                "embedding_available": False,
                "error": str(e)
            }
    
    def compare_with_references(self, essay_text: str, reference_essays: List[str]) -> Dict[str, Any]:
        """
        Compare essay with specific reference essays
        """
        if not self.is_available():
            return {
                "similarities": [],
                "average_similarity": 0.0,
                "embedding_available": False,
                "error": "Embedding similarity service not available"
            }
        
        try:
            similarities = []
            
            # Get embedding for the input essay
            essay_embedding = self.similarity_engine.get_embedding(essay_text)
            
            # Compare with each reference essay
            for i, ref_essay in enumerate(reference_essays):
                ref_embedding = self.similarity_engine.get_embedding(ref_essay)
                
                # Calculate cosine similarity
                from sklearn.metrics.pairwise import cosine_similarity
                import numpy as np
                
                similarity = cosine_similarity([essay_embedding], [ref_embedding])[0][0]
                
                similarities.append({
                    "reference_index": i,
                    "similarity_score": float(similarity),
                    "similarity_percentage": round(float(similarity) * 100, 1),
                    "reference_preview": ref_essay[:100] + "..." if len(ref_essay) > 100 else ref_essay
                })
            
            # Calculate statistics
            similarity_scores = [s["similarity_score"] for s in similarities]
            average_similarity = sum(similarity_scores) / len(similarity_scores) if similarity_scores else 0
            
            # Find best and worst matches
            best_match = max(similarities, key=lambda x: x["similarity_score"]) if similarities else None
            worst_match = min(similarities, key=lambda x: x["similarity_score"]) if similarities else None
            
            return {
                "similarities": similarities,
                "statistics": {
                    "average_similarity": round(average_similarity, 3),
                    "average_percentage": round(average_similarity * 100, 1),
                    "max_similarity": round(max(similarity_scores), 3) if similarity_scores else 0,
                    "min_similarity": round(min(similarity_scores), 3) if similarity_scores else 0,
                    "best_match": best_match,
                    "worst_match": worst_match
                },
                "embedding_available": True,
                "total_references": len(reference_essays)
            }
            
        except Exception as e:
            return {
                "similarities": [],
                "average_similarity": 0.0,
                "embedding_available": False,
                "error": str(e)
            }
    
    def get_cache_status(self) -> Optional[Dict[str, Any]]:
        """Get embedding cache status"""
        if not self.is_available():
            return None
        
        try:
            cache_file = 'data/embedding_cache.pkl'
            if os.path.exists(cache_file):
                import pickle
                with open(cache_file, 'rb') as f:
                    cache = pickle.load(f)
                
                return {
                    "cache_exists": True,
                    "cached_embeddings": len(cache),
                    "cache_file_size": os.path.getsize(cache_file)
                }
            else:
                return {
                    "cache_exists": False,
                    "cached_embeddings": 0,
                    "cache_file_size": 0
                }
        except Exception:
            return {
                "cache_exists": False,
                "cached_embeddings": 0,
                "cache_file_size": 0,
                "error": "Could not read cache status"
            }
    
    def get_reference_count(self) -> int:
        """Get number of reference essays available"""
        if not self.is_available():
            return 0
        
        try:
            if hasattr(self.similarity_engine, 'reference_essays'):
                return sum(len(essays) for essays in self.similarity_engine.reference_essays.values())
            return 0
        except Exception:
            return 0
    
    def clear_cache(self) -> int:
        """Clear embedding cache and return number of cleared embeddings"""
        if not self.is_available():
            return 0
        
        try:
            cache_file = 'data/embedding_cache.pkl'
            cleared_count = 0
            
            if os.path.exists(cache_file):
                import pickle
                with open(cache_file, 'rb') as f:
                    cache = pickle.load(f)
                cleared_count = len(cache)
                
                # Clear the cache
                os.remove(cache_file)
            
            # Also clear in-memory cache if available
            if hasattr(self.similarity_engine, 'embedding_cache'):
                self.similarity_engine.embedding_cache.clear()
            
            return cleared_count
            
        except Exception as e:
            raise Exception(f"Failed to clear cache: {str(e)}")
    
    def get_similarity_metrics(self, essay_text: str) -> Dict[str, Any]:
        """
        Get detailed similarity metrics for an essay
        """
        if not self.is_available():
            return {
                "embedding_available": False,
                "error": "Embedding similarity service not available"
            }
        
        try:
            # Get basic similarity analysis
            similarity_result = self.analyze_similarity(essay_text)
            
            # Add additional metrics
            word_count = len(essay_text.split())
            char_count = len(essay_text)
            
            # Calculate text statistics
            sentences = essay_text.split('.')
            sentence_count = len([s for s in sentences if s.strip()])
            avg_sentence_length = word_count / sentence_count if sentence_count > 0 else 0
            
            similarity_result.update({
                "text_statistics": {
                    "word_count": word_count,
                    "character_count": char_count,
                    "sentence_count": sentence_count,
                    "average_sentence_length": round(avg_sentence_length, 1)
                },
                "analysis_timestamp": None  # Could add timestamp if needed
            })
            
            return similarity_result
            
        except Exception as e:
            return {
                "embedding_available": False,
                "error": str(e)
            }
