#!/usr/bin/env python3
"""
Scholarship Criteria Evaluation System
Supports multiple scholarships with specific criteria and embedding similarity
"""

import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

class MatchLevel(Enum):
    HIGH = "high"
    MEDIUM = "medium" 
    LOW = "low"

@dataclass
class ScholarshipType:
    id: str
    name: str
    logo: str
    description: str

@dataclass
class EssayType:
    id: str
    name: str
    scholarship_id: str
    criteria: List[str]
    instructions: str
    max_words: Optional[int] = None

@dataclass
class CriteriaMatch:
    criteria: str
    match_level: MatchLevel
    explanation: str
    score: int  # 0-100

@dataclass
class CriteriaCheckResult:
    content: str
    overall_score: int
    matches: List[CriteriaMatch]
    embedding_available: bool = False
    analysis_details: Dict[str, Any] = None

# Scholarship definitions
SCHOLARSHIP_TYPES = [
    ScholarshipType(
        id="gks",
        name="Global Korea Scholarship",
        logo="🇰🇷",
        description="Korean government scholarship for international students"
    ),
    ScholarshipType(
        id="chevening",
        name="Chevening Scholarship", 
        logo="🇬🇧",
        description="UK government scholarship program"
    ),
    ScholarshipType(
        id="fulbright",
        name="Fulbright Program",
        logo="🇺🇸", 
        description="US government educational exchange program"
    ),
    ScholarshipType(
        id="daad",
        name="DAAD Scholarship",
        logo="🇩🇪",
        description="German Academic Exchange Service scholarship"
    ),
    ScholarshipType(
        id="erasmus",
        name="Erasmus+ Programme",
        logo="🇪🇺",
        description="European Union student exchange program"
    )
]

# Essay type definitions with specific criteria
ESSAY_TYPES = [
    # GKS Essays
    EssayType(
        id="personal_statement",
        name="Personal Statement",
        scholarship_id="gks",
        criteria=[
            "Motivations with which you apply for this program",
            "Educational background",
            "Significant experiences you have had; persons or events that have had a significant influence on you",
            "Extracurricular activities such as club activities, community service activities or work experiences",
            "Awards you have received, publications you have made, or skills you have acquired"
        ],
        instructions="Please type in Korean or in English. The essay must be single spaced within TWO pages (A4 size, Times New Roman, 11pt). Please do not exceed the limit as any excess part will be excluded from evaluation.",
        max_words=1000
    ),
    EssayType(
        id="study_plan",
        name="Study Plan",
        scholarship_id="gks",
        criteria=[
            "Specific academic goals and research interests",
            "Detailed study plan for the degree program",
            "How the program aligns with your career objectives",
            "Research methodology and approach",
            "Expected outcomes and contributions to your field"
        ],
        instructions="Describe your academic goals, research interests, and detailed study plan.",
        max_words=1200
    ),
    
    # Chevening Essays
    EssayType(
        id="leadership_essay",
        name="Leadership Essay",
        scholarship_id="chevening",
        criteria=[
            "Demonstration of leadership potential",
            "Specific examples of leadership roles",
            "Impact of your leadership on others",
            "Leadership style and approach",
            "Future leadership aspirations"
        ],
        instructions="Describe your leadership experience and potential (minimum 100 words, maximum 500 words).",
        max_words=500
    ),
    EssayType(
        id="networking_essay", 
        name="Networking Essay",
        scholarship_id="chevening",
        criteria=[
            "Ability to build and maintain relationships",
            "Examples of successful networking",
            "How you engage with diverse groups",
            "Professional network development",
            "Plans for expanding your network in the UK"
        ],
        instructions="Explain how you build and maintain relationships (minimum 100 words, maximum 500 words).",
        max_words=500
    ),
    EssayType(
        id="career_goals_essay",
        name="Career Goals Essay", 
        scholarship_id="chevening",
        criteria=[
            "Clear short-term career objectives",
            "Long-term career vision",
            "How Chevening will help achieve goals",
            "Contribution to your home country",
            "Specific career milestones and timeline"
        ],
        instructions="Outline your career goals and how Chevening will help you achieve them (minimum 100 words, maximum 500 words).",
        max_words=500
    ),
    
    # Fulbright Essays
    EssayType(
        id="statement_of_purpose",
        name="Statement of Purpose",
        scholarship_id="fulbright",
        criteria=[
            "Academic and professional background",
            "Specific objectives for study in the US",
            "How the program fits your goals",
            "Research interests and methodology",
            "Expected impact and contributions"
        ],
        instructions="Describe your academic and professional background, objectives, and how the program fits your goals.",
        max_words=1000
    ),
    EssayType(
        id="personal_statement_fulbright",
        name="Personal Statement",
        scholarship_id="fulbright", 
        criteria=[
            "Personal background and experiences",
            "Motivation for international study",
            "Cultural adaptability and openness",
            "Personal growth and development",
            "Commitment to cross-cultural exchange"
        ],
        instructions="Describe your personal background, experiences, and motivation for international study.",
        max_words=800
    ),
    
    # DAAD Essays
    EssayType(
        id="motivation_letter",
        name="Motivation Letter",
        scholarship_id="daad",
        criteria=[
            "Motivation for studying in Germany",
            "Academic and professional qualifications",
            "Specific program and university choice",
            "Career plans and objectives",
            "Contribution to German-home country relations"
        ],
        instructions="Explain your motivation for studying in Germany and your qualifications.",
        max_words=800
    ),
    
    # Erasmus Essays
    EssayType(
        id="motivation_letter_erasmus",
        name="Motivation Letter",
        scholarship_id="erasmus",
        criteria=[
            "Motivation for European study experience",
            "Academic goals and learning objectives",
            "Cultural interest and adaptability",
            "Language skills and development",
            "Contribution to European integration"
        ],
        instructions="Describe your motivation for studying in Europe and expected learning outcomes.",
        max_words=600
    )
]

class ScholarshipCriteriaEvaluator:
    """
    Evaluates essays against scholarship-specific criteria using embedding similarity
    """
    
    def __init__(self):
        self.scholarships = {s.id: s for s in SCHOLARSHIP_TYPES}
        self.essay_types = {f"{e.scholarship_id}_{e.id}": e for e in ESSAY_TYPES}
        
        # Load embedding similarity if available
        try:
            from embedding_similarity import compute_embedding_similarity
            self.embedding_available = True
            self.compute_embedding_similarity = compute_embedding_similarity
        except ImportError:
            self.embedding_available = False
            self.compute_embedding_similarity = None
    
    def get_scholarships(self) -> List[Dict[str, str]]:
        """Get all available scholarships"""
        return [
            {
                "id": s.id,
                "name": s.name, 
                "logo": s.logo,
                "description": s.description
            }
            for s in SCHOLARSHIP_TYPES
        ]
    
    def get_essay_types(self, scholarship_id: str) -> List[Dict[str, Any]]:
        """Get essay types for a specific scholarship"""
        essay_types = [e for e in ESSAY_TYPES if e.scholarship_id == scholarship_id]
        return [
            {
                "id": e.id,
                "name": e.name,
                "scholarship_id": e.scholarship_id,
                "criteria": e.criteria,
                "instructions": e.instructions,
                "max_words": e.max_words,
                "criteria_count": len(e.criteria)
            }
            for e in essay_types
        ]
    
    def get_essay_type_details(self, scholarship_id: str, essay_type_id: str) -> Optional[EssayType]:
        """Get specific essay type details"""
        key = f"{scholarship_id}_{essay_type_id}"
        return self.essay_types.get(key)
    
    def analyze_criteria(self, text: str, scholarship_id: str, essay_type_id: str) -> CriteriaCheckResult:
        """
        Analyze essay text against scholarship criteria with embedding similarity
        """
        if not text.strip():
            raise ValueError("Essay text cannot be empty")
        
        # Get essay type details
        essay_type = self.get_essay_type_details(scholarship_id, essay_type_id)
        if not essay_type:
            raise ValueError(f"Essay type not found: {scholarship_id}_{essay_type_id}")
        
        # Get embedding similarity analysis if available
        embedding_results = None
        if self.embedding_available and self.compute_embedding_similarity:
            try:
                embedding_results = self.compute_embedding_similarity(text)
            except Exception as e:
                print(f"Warning: Embedding similarity failed: {e}")
        
        # Analyze each criterion
        matches = []
        total_score = 0
        
        for i, criterion in enumerate(essay_type.criteria):
            # Get embedding score for this criterion if available
            embedding_score = None
            if embedding_results and embedding_results.get("embedding_available"):
                criterion_scores = embedding_results.get("criterion_scores", {})
                # Map criteria to standard names for embedding lookup
                criterion_mapping = {
                    "Motivations with which you apply for this program": "Motivation",
                    "Educational background": "Educational Background", 
                    "Significant experiences you have had; persons or events that have had a significant influence on you": "Introduction/Hook",
                    "Extracurricular activities such as club activities, community service activities or work experiences": "Extracurricular",
                    "Awards you have received, publications you have made, or skills you have acquired": "Educational Background"
                }
                
                mapped_criterion = criterion_mapping.get(criterion, "Motivation")
                if mapped_criterion in criterion_scores:
                    embedding_score = criterion_scores[mapped_criterion]["score"]
            
            # Calculate criterion score (combine rule-based + embedding if available)
            if embedding_score is not None:
                # Use embedding score as primary, with some rule-based adjustments
                base_score = embedding_score
                
                # Apply text-based adjustments
                text_lower = text.lower()
                criterion_lower = criterion.lower()
                
                # Keyword presence bonus
                if any(word in text_lower for word in criterion_lower.split()[:3]):
                    base_score = min(100, base_score + 5)
                
                # Length appropriateness
                word_count = len(text.split())
                if essay_type.max_words:
                    if word_count > essay_type.max_words * 1.2:  # 20% over limit
                        base_score = max(0, base_score - 10)
                    elif word_count < essay_type.max_words * 0.5:  # Less than 50% of target
                        base_score = max(0, base_score - 15)
                
                final_score = int(base_score)
            else:
                # Fallback to rule-based scoring
                final_score = self._rule_based_scoring(text, criterion, essay_type)
            
            # Determine match level and explanation
            if final_score >= 80:
                match_level = MatchLevel.HIGH
                explanation = f"Excellent coverage of {criterion.lower()}. "
                if embedding_score is not None:
                    explanation += f"High semantic similarity to successful examples (similarity score: {embedding_score:.1f})."
                else:
                    explanation += "Strong evidence and detailed examples provided."
            elif final_score >= 60:
                match_level = MatchLevel.MEDIUM
                explanation = f"Good coverage of {criterion.lower()}. "
                if embedding_score is not None:
                    explanation += f"Moderate semantic similarity to successful examples (similarity score: {embedding_score:.1f})."
                else:
                    explanation += "Some evidence provided but could be more detailed."
            else:
                match_level = MatchLevel.LOW
                explanation = f"Limited coverage of {criterion.lower()}. "
                if embedding_score is not None:
                    explanation += f"Low semantic similarity to successful examples (similarity score: {embedding_score:.1f})."
                else:
                    explanation += "Needs more specific examples and detailed explanation."
            
            matches.append(CriteriaMatch(
                criteria=criterion,
                match_level=match_level,
                explanation=explanation,
                score=final_score
            ))
            
            total_score += final_score
        
        # Calculate overall score
        overall_score = int(total_score / len(essay_type.criteria)) if essay_type.criteria else 0
        
        # Prepare analysis details
        analysis_details = {
            "word_count": len(text.split()),
            "max_words": essay_type.max_words,
            "scholarship": self.scholarships[scholarship_id].name,
            "essay_type": essay_type.name,
            "criteria_count": len(essay_type.criteria)
        }
        
        if embedding_results:
            analysis_details["embedding_similarity"] = embedding_results.get("overall_similarity_score", 0)
            analysis_details["embedding_details"] = embedding_results.get("similarity_details", {})
        
        return CriteriaCheckResult(
            content="Analysis completed successfully",
            overall_score=overall_score,
            matches=matches,
            embedding_available=self.embedding_available and embedding_results is not None,
            analysis_details=analysis_details
        )
    
    def _rule_based_scoring(self, text: str, criterion: str, essay_type: EssayType) -> int:
        """
        Fallback rule-based scoring when embedding similarity is not available
        """
        score = 50  # Base score
        text_lower = text.lower()
        criterion_lower = criterion.lower()
        
        # Keyword matching
        criterion_keywords = criterion_lower.split()[:5]  # First 5 words
        keyword_matches = sum(1 for word in criterion_keywords if word in text_lower)
        score += (keyword_matches / len(criterion_keywords)) * 20
        
        # Length appropriateness
        word_count = len(text.split())
        if essay_type.max_words:
            target_words = essay_type.max_words
            if 0.7 * target_words <= word_count <= 1.1 * target_words:
                score += 10
            elif word_count < 0.5 * target_words:
                score -= 20
        
        # Specific criterion adjustments
        if "motivation" in criterion_lower:
            motivation_words = ["goal", "aspire", "dream", "vision", "objective", "aim"]
            if any(word in text_lower for word in motivation_words):
                score += 15
        
        if "education" in criterion_lower:
            education_words = ["university", "degree", "gpa", "course", "study", "academic"]
            if any(word in text_lower for word in education_words):
                score += 15
        
        if "experience" in criterion_lower:
            experience_words = ["experience", "internship", "work", "project", "research"]
            if any(word in text_lower for word in experience_words):
                score += 15
        
        if "extracurricular" in criterion_lower or "activities" in criterion_lower:
            activity_words = ["club", "volunteer", "leadership", "team", "organization"]
            if any(word in text_lower for word in activity_words):
                score += 15
        
        return max(0, min(100, int(score)))

# Global instance
scholarship_evaluator = ScholarshipCriteriaEvaluator()
