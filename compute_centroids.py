import json
import numpy as np
import os
from utils import embed
from collections import defaultdict

def compute_centroids():
    """
    Compute centroids and standard deviations from winner essays
    and save them to centroids.npy
    """
    print("Computing centroids from winner essays...")
    
    # Check if data directory exists
    if not os.path.exists('data'):
        os.makedirs('data')
        print("Created data directory")
    
    # Load winners data
    try:
        with open('data/winners.json', 'r') as f:
            winners = json.load(f)
    except FileNotFoundError:
        print("Error: data/winners.json not found")
        return
    
    # Group by criterion
    criterion_texts = defaultdict(list)
    for winner in winners:
        criterion_texts[winner['criterion']].append(winner['raw_text'])
    
    # Compute embeddings and centroids
    centroids = {}
    embeddings_by_criterion = {}
    
    for criterion, texts in criterion_texts.items():
        print(f"Processing {criterion}...")
        embeddings = []
        
        for text in texts:
            try:
                embedding = embed(text)
                embeddings.append(embedding)
            except Exception as e:
                print(f"Error embedding text for {criterion}: {e}")
        
        if embeddings:
            embeddings_array = np.array(embeddings)
            
            # Compute centroid (mean vector)
            centroid = np.mean(embeddings_array, axis=0)
            
            # Compute standard deviation of cosine similarities
            similarities = []
            for emb in embeddings:
                sim = np.dot(emb, centroid) / (np.linalg.norm(emb) * np.linalg.norm(centroid))
                similarities.append(sim)
            
            std_dev = np.std(similarities) if len(similarities) > 1 else 0.1
            
            centroids[criterion] = {
                "μ": centroid,
                "σ": std_dev
            }
            
            embeddings_by_criterion[criterion] = embeddings_array
    
    # Save centroids
    np.save('data/centroids.npy', centroids)
    
    # Save embeddings
    np.save('data/winners.vec.npy', embeddings_by_criterion)
    
    print("Centroids computed and saved to data/centroids.npy")
    print("Embeddings saved to data/winners.vec.npy")

if __name__ == "__main__":
    compute_centroids()