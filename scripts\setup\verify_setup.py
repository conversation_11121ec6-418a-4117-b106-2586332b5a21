import os
import sys

def check_files():
    """Check if required files exist"""
    required_files = ['app.py', 'evaluation.py', 'requirements.txt', '.env']
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} exists")
        else:
            print(f"❌ {file} missing")
            missing_files.append(file)
    
    return len(missing_files) == 0

def check_env():
    """Check environment variables"""
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv('OPENROUTER_API_KEY')
    if api_key and api_key != 'your_openrouter_api_key_here':
        print("✅ OpenRouter API key is set")
        return True
    else:
        print("❌ OpenRouter API key not set or still default")
        return False

def check_imports():
    """Check if imports work"""
    try:
        from evaluation import evaluate
        print("✅ evaluation.py imports successfully")
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_basic_function():
    """Test basic evaluation function"""
    try:
        from evaluation import fast_rules
        score = fast_rules("This is a test essay about studying in Korea. I want to pursue my goals and dreams.")
        print(f"✅ Basic function test passed (score: {score})")
        return True
    except Exception as e:
        print(f"❌ Function test failed: {e}")
        return False

if __name__ == '__main__':
    print("🔍 Verifying Essay Evaluation Service Setup")
    print("=" * 50)
    
    print("\n📁 Checking files...")
    files_ok = check_files()
    
    print("\n🔑 Checking environment...")
    env_ok = check_env()
    
    print("\n📦 Checking imports...")
    imports_ok = check_imports()
    
    print("\n🧪 Testing basic functions...")
    function_ok = test_basic_function()
    
    print("\n" + "=" * 50)
    if all([files_ok, imports_ok, function_ok]):
        print("✅ Setup verification passed!")
        print("\nYou can now run:")
        print("  python app.py")
        if not env_ok:
            print("\n⚠️  Note: Add your OpenRouter API key to .env for full functionality")
    else:
        print("❌ Setup verification failed!")
        print("\nPlease fix the issues above and try again.")