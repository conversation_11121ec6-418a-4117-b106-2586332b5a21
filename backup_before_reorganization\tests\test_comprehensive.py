#!/usr/bin/env python3
"""
Comprehensive test suite for the Essay Evaluation Service
Tests all components: evaluation logic, API endpoints, error handling, and edge cases
"""

import pytest
import json
import os
import sys
from unittest.mock import patch, MagicMock
import requests
from flask import Flask

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from evaluation import evaluate, fast_rules, enhanced_fast_rules, llm_scores

class TestEvaluationLogic:
    """Test the core evaluation logic"""

    def test_fast_rules_basic(self):
        """Test basic fast rules scoring"""
        # Good essay
        good_essay = """
        I am passionate about studying artificial intelligence in Korea because of its advanced technology sector.
        My goal is to pursue a Master's degree at KAIST to research machine learning applications.

        During my undergraduate studies in Computer Science, I maintained a 3.8 GPA and published research papers.
        I have experience in programming, data analysis, and software development.

        As president of the coding club, I organized hackathons and mentored junior students.
        I also volunteered at local schools teaching programming to children.

        After graduation, I plan to return to my home country and establish a tech startup
        that leverages Korean AI innovations to solve local problems.
        """

        score = fast_rules(good_essay)
        assert 50 <= score <= 100, f"Expected score 50-100, got {score}"

    def test_fast_rules_edge_cases(self):
        """Test edge cases for fast rules"""
        # Empty essay
        assert fast_rules("") == 0

        # Very short essay
        short_essay = "I want to study in Korea."
        score = fast_rules(short_essay)
        assert score < 50, f"Short essay should score low, got {score}"

        # Very long essay (over 1200 words)
        long_essay = " ".join(["word"] * 1300)
        score = fast_rules(long_essay)
        assert score < 90, f"Long essay should be penalized, got {score}"

    def test_enhanced_fast_rules(self):
        """Test enhanced fast rules with detailed analysis"""
        essay = """
        My dream is to study computer science in Korea at Seoul National University.
        I have always been passionate about technology and innovation.

        During my studies, I learned programming languages like Python and Java.
        I also participated in various coding competitions and won several awards.

        In the future, I plan to work for a Korean tech company like Samsung or LG.
        I believe Korea is the perfect place to advance my career in technology.
        """

        result = enhanced_fast_rules(essay)

        assert isinstance(result, dict)
        assert "score" in result
        assert "feedback" in result
        assert "details" in result
        assert 0 <= result["score"] <= 100
        assert isinstance(result["details"], dict)
        assert "word_count" in result["details"]

class TestAPIEndpoints:
    """Test Flask API endpoints"""

    @pytest.fixture
    def client(self):
        """Create test client"""
        app.config['TESTING'] = True
        with app.test_client() as client:
            yield client

    def test_health_endpoint(self, client):
        """Test health check endpoint"""
        response = client.get('/health')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data['status'] == 'healthy'
        assert 'service' in data
        assert 'version' in data

    def test_criteria_endpoint(self, client):
        """Test criteria information endpoint"""
        response = client.get('/api/v1/criteria')
        assert response.status_code == 200

        data = json.loads(response.data)
        assert 'criteria' in data
        assert 'scoring_scale' in data
        assert len(data['criteria']) == 5  # Should have 5 criteria

    def test_evaluate_endpoint_success(self, client):
        """Test successful evaluation"""
        essay = """
        I am passionate about studying in Korea because of its technological advancement.
        My goal is to pursue a Master's degree in Computer Science at KAIST.

        During my undergraduate studies, I maintained excellent grades and conducted research.
        I have published papers and won academic awards for my work.

        As a leader in student organizations, I organized events and mentored peers.
        I also volunteered in community service projects regularly.

        After graduation, I plan to contribute to Korea's tech industry and eventually
        return to my country to share the knowledge and experience I gained.
        """

        response = client.post('/api/v1/evaluate',
                             json={'essay': essay},
                             content_type='application/json')

        assert response.status_code == 200

        data = json.loads(response.data)
        assert 'overall' in data
        assert 'criteria' in data
        assert 'word_count' in data
        assert 'evaluation_timestamp' in data
        assert 0 <= data['overall'] <= 100

    def test_evaluate_endpoint_errors(self, client):
        """Test error cases for evaluation endpoint"""
        # Missing essay field
        response = client.post('/api/v1/evaluate',
                             json={},
                             content_type='application/json')
        assert response.status_code == 400

        # Empty essay
        response = client.post('/api/v1/evaluate',
                             json={'essay': ''},
                             content_type='application/json')
        assert response.status_code == 400

        # Non-string essay
        response = client.post('/api/v1/evaluate',
                             json={'essay': 123},
                             content_type='application/json')
        assert response.status_code == 400

        # Essay too long
        long_essay = 'a' * 10001
        response = client.post('/api/v1/evaluate',
                             json={'essay': long_essay},
                             content_type='application/json')
        assert response.status_code == 400

    def test_analyze_endpoint(self, client):
        """Test structure analysis endpoint"""
        essay = "This is a test essay for structure analysis. It has multiple sentences."

        response = client.post('/api/v1/analyze',
                             json={'essay': essay},
                             content_type='application/json')

        assert response.status_code == 200

        data = json.loads(response.data)
        assert 'structure_score' in data
        assert 'feedback' in data
        assert 'details' in data
        assert 'analysis_type' in data
        assert data['analysis_type'] == 'structure_only'

    def test_404_handler(self, client):
        """Test 404 error handler"""
        response = client.get('/nonexistent')
        assert response.status_code == 404

        data = json.loads(response.data)
        assert data['code'] == 'NOT_FOUND'
        assert 'available_endpoints' in data

class TestEnvironmentAndConfig:
    """Test environment configuration and setup"""

    def test_env_file_exists(self):
        """Test that .env file exists"""
        assert os.path.exists('.env'), ".env file should exist"

    def test_required_env_vars(self):
        """Test that required environment variables are set"""
        from dotenv import load_dotenv
        load_dotenv()

        api_key = os.getenv('OPENROUTER_API_KEY')
        assert api_key is not None, "OPENROUTER_API_KEY should be set"
        assert api_key != 'your_openrouter_api_key_here', "API key should not be default value"

        base_url = os.getenv('OPENROUTER_BASE')
        assert base_url is not None, "OPENROUTER_BASE should be set"

        model_id = os.getenv('MODEL_ID')
        assert model_id is not None, "MODEL_ID should be set"

class TestIntegration:
    """Integration tests that test the full workflow"""

    @patch('evaluation.requests.post')
    def test_llm_scores_mock(self, mock_post):
        """Test LLM scoring with mocked API response"""
        # Mock successful API response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'choices': [{
                'message': {
                    'content': json.dumps({
                        "Introduction/Hook": {"score": 85, "feedback": "Good opening"},
                        "Motivation": {"score": 80, "feedback": "Clear goals"},
                        "Educational Background": {"score": 88, "feedback": "Strong background"},
                        "Extracurricular": {"score": 82, "feedback": "Good activities"}
                    })
                }
            }]
        }
        mock_post.return_value = mock_response

        essay = "Test essay for LLM scoring"
        result = llm_scores(essay)

        assert isinstance(result, dict)
        assert "Introduction/Hook" in result
        assert "Motivation" in result
        assert "Educational Background" in result
        assert "Extracurricular" in result

    def test_full_evaluation_workflow(self):
        """Test the complete evaluation workflow"""
        essay = """
        The moment I first programmed a simple calculator in Python during my freshman year,
        I realized that coding was more than just writing instructions for a computer—it was
        about solving real-world problems and creating innovative solutions.

        My goal is to pursue a Master's degree in Computer Science at Seoul National University,
        specializing in artificial intelligence and machine learning. Korea's position as a
        global leader in technology, with companies like Samsung and LG at the forefront of
        innovation, makes it the ideal place for my advanced studies.

        During my undergraduate studies in Software Engineering at the University of Technology,
        I maintained a GPA of 3.7/4.0 while working on several research projects. My thesis on
        "Deep Learning Applications in Natural Language Processing" was selected for presentation
        at the International Conference on AI and received recognition for its innovative approach.

        Beyond academics, I served as president of the Computer Science Student Association,
        where I organized coding workshops for over 200 students and established partnerships
        with local tech companies for internship opportunities. I also volunteered as a
        programming instructor for underprivileged youth in my community.

        Upon completing my studies in Korea, I plan to return to my home country and establish
        a research lab focused on developing AI solutions for local challenges, particularly
        in education and healthcare, while maintaining strong collaborative ties with Korean
        institutions and companies.
        """

        # This should work even if LLM API fails (fallback mode)
        result = evaluate(essay)

        assert isinstance(result, dict)
        assert 'overall' in result
        assert 'criteria' in result
        assert 'word_count' in result
        assert 'evaluation_timestamp' in result
        assert 0 <= result['overall'] <= 100

        # Check that all expected criteria are present
        expected_criteria = ["Format Compliance", "Introduction/Hook", "Motivation",
                           "Educational Background", "Extracurricular"]
        for criterion in expected_criteria:
            assert criterion in result['criteria']
            assert 'score' in result['criteria'][criterion]
            assert 'feedback' in result['criteria'][criterion]

if __name__ == '__main__':
    # Run tests with verbose output
    pytest.main([__file__, '-v', '--tb=short'])
