#!/usr/bin/env python3
"""
Master test runner for Essay Evaluation Service
Orchestrates all testing phases and provides comprehensive reporting
"""

import os
import sys
import subprocess
import time
import json
import argparse
from pathlib import Path

def run_command(command, description, capture_output=True):
    """Run a command and return success status"""
    print(f"🔄 {description}...")
    
    try:
        if capture_output:
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {description} - SUCCESS")
                return True, result.stdout
            else:
                print(f"❌ {description} - FAILED")
                print(f"Error: {result.stderr}")
                return False, result.stderr
        else:
            result = subprocess.run(command, shell=True)
            if result.returncode == 0:
                print(f"✅ {description} - SUCCESS")
                return True, ""
            else:
                print(f"❌ {description} - FAILED")
                return False, ""
    except Exception as e:
        print(f"❌ {description} - ERROR: {e}")
        return False, str(e)

def main():
    parser = argparse.ArgumentParser(description='Essay Evaluation Service Test Runner')
    parser.add_argument('--quick', action='store_true', help='Run quick tests only')
    parser.add_argument('--no-install', action='store_true', help='Skip dependency installation')
    parser.add_argument('--env-only', action='store_true', help='Only validate environment')
    parser.add_argument('--comprehensive', action='store_true', help='Run all tests including performance')
    
    args = parser.parse_args()
    
    print("🚀 Essay Evaluation Service - Test Runner")
    print("=" * 50)
    
    start_time = time.time()
    test_results = {}
    
    # Phase 1: Environment Validation
    print("\n📋 Phase 1: Environment Validation")
    print("-" * 30)
    
    success, output = run_command(f"{sys.executable} validate_env.py", "Environment validation")
    test_results['environment'] = success
    
    if args.env_only:
        print(f"\n⏱️  Total time: {time.time() - start_time:.2f} seconds")
        return 0 if success else 1
    
    if not success:
        print("❌ Environment validation failed. Please fix issues before continuing.")
        return 1
    
    # Phase 2: Dependency Installation
    if not args.no_install:
        print("\n📦 Phase 2: Dependency Installation")
        print("-" * 30)
        
        success, output = run_command(f"{sys.executable} -m pip install -r requirements.txt", 
                                    "Installing dependencies")
        test_results['dependencies'] = success
        
        if not success:
            print("❌ Dependency installation failed.")
            return 1
    else:
        test_results['dependencies'] = True
        print("\n📦 Phase 2: Skipping dependency installation")
    
    # Phase 3: Unit Tests
    print("\n🧪 Phase 3: Unit Tests")
    print("-" * 30)
    
    # Try pytest first, fall back to basic tests
    success, output = run_command(f"{sys.executable} -m pytest test_comprehensive.py -v", 
                                "Running pytest unit tests")
    
    if not success:
        print("⚠️  Pytest failed, trying basic tests...")
        success, output = run_command(f"{sys.executable} test_comprehensive.py", 
                                    "Running basic unit tests")
    
    test_results['unit_tests'] = success
    
    if args.quick:
        print(f"\n⏱️  Total time: {time.time() - start_time:.2f} seconds")
        print("\n📊 Quick Test Summary:")
        for phase, result in test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"  {phase}: {status}")
        return 0 if all(test_results.values()) else 1
    
    # Phase 4: Integration Tests
    print("\n🔗 Phase 4: Integration Tests")
    print("-" * 30)
    
    if args.comprehensive:
        success, output = run_command(f"{sys.executable} automate_test.py", 
                                    "Running comprehensive automation tests", 
                                    capture_output=False)
    else:
        success, output = run_command(f"{sys.executable} test_simple.py", 
                                    "Running simple integration tests")
    
    test_results['integration_tests'] = success
    
    # Phase 5: Performance Tests (only in comprehensive mode)
    if args.comprehensive:
        print("\n⚡ Phase 5: Performance Tests")
        print("-" * 30)
        
        # Create a simple performance test
        perf_test_script = """
import requests
import time
import json

def test_performance():
    base_url = "http://localhost:5000"
    
    # Test essays of different lengths
    essays = {
        "short": "I want to study in Korea.",
        "medium": "I want to study computer science in Korea because of its advanced technology. My goal is to pursue a Master's degree and contribute to innovation.",
        "long": " ".join(["This is a comprehensive essay about studying in Korea."] * 50)
    }
    
    results = {}
    
    for essay_type, essay_text in essays.items():
        start_time = time.time()
        try:
            response = requests.post(f"{base_url}/api/v1/evaluate",
                                   json={"essay": essay_text},
                                   timeout=30)
            end_time = time.time()
            
            if response.status_code == 200:
                results[essay_type] = {
                    "time": round(end_time - start_time, 2),
                    "status": "success"
                }
                print(f"✅ {essay_type} essay: {results[essay_type]['time']}s")
            else:
                results[essay_type] = {"status": "failed", "code": response.status_code}
                print(f"❌ {essay_type} essay failed: {response.status_code}")
        except Exception as e:
            results[essay_type] = {"status": "error", "error": str(e)}
            print(f"❌ {essay_type} essay error: {e}")
    
    return all(r.get("status") == "success" for r in results.values())

if __name__ == "__main__":
    import sys
    success = test_performance()
    sys.exit(0 if success else 1)
"""
        
        with open("temp_perf_test.py", "w") as f:
            f.write(perf_test_script)
        
        success, output = run_command(f"{sys.executable} temp_perf_test.py", 
                                    "Running performance tests")
        test_results['performance_tests'] = success
        
        # Clean up
        try:
            os.remove("temp_perf_test.py")
        except:
            pass
    
    # Generate final report
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n⏱️  Total execution time: {total_time:.2f} seconds")
    
    # Create detailed report
    report = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "execution_time": round(total_time, 2),
        "test_mode": "comprehensive" if args.comprehensive else "standard",
        "results": test_results,
        "summary": {
            "total_phases": len(test_results),
            "passed": sum(1 for r in test_results.values() if r),
            "failed": sum(1 for r in test_results.values() if not r),
            "success_rate": round(sum(1 for r in test_results.values() if r) / len(test_results) * 100, 1)
        }
    }
    
    # Save report
    with open("test_results.json", "w") as f:
        json.dump(report, f, indent=2)
    
    # Print summary
    print("\n📊 Final Test Summary")
    print("=" * 50)
    
    for phase, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {phase.replace('_', ' ').title()}: {status}")
    
    print(f"\n🎯 Overall Success Rate: {report['summary']['success_rate']}%")
    print(f"📄 Detailed report saved to: test_results.json")
    
    if all(test_results.values()):
        print("\n🎉 All tests passed! The Essay Evaluation Service is ready for production.")
        print("🚀 Start the service with: python app.py")
    else:
        print("\n⚠️  Some tests failed. Please review the output above.")
    
    return 0 if all(test_results.values()) else 1

if __name__ == '__main__':
    sys.exit(main())
