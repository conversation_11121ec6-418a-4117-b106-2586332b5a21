#!/usr/bin/env python3
"""
Test script for scholarship criteria evaluation system
Tests the complete frontend-backend integration
"""

import requests
import json
import time
from typing import Dict, Any

class ScholarshipCriteriaDemo:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        
    def print_header(self, title):
        print(f"\n{'='*70}")
        print(f"{title.center(70)}")
        print(f"{'='*70}")
    
    def print_section(self, title):
        print(f"\n{'-'*50}")
        print(f"{title}")
        print(f"{'-'*50}")
    
    def check_service_health(self):
        """Check if the service is running"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Service is healthy: {data['service']} v{data['version']}")
                return True
            else:
                print(f"❌ Service health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Cannot connect to service: {e}")
            print(f"   Make sure the service is running at {self.base_url}")
            return False
    
    def test_get_scholarships(self):
        """Test getting all available scholarships"""
        try:
            response = requests.get(f"{self.base_url}/api/v1/scholarships")
            if response.status_code == 200:
                data = response.json()
                scholarships = data.get("scholarships", [])
                print(f"✅ Found {len(scholarships)} scholarships:")
                for scholarship in scholarships:
                    print(f"   {scholarship['logo']} {scholarship['name']} ({scholarship['id']})")
                    print(f"      {scholarship['description']}")
                return scholarships
            else:
                print(f"❌ Failed to get scholarships: {response.status_code}")
                return []
        except Exception as e:
            print(f"❌ Error getting scholarships: {e}")
            return []
    
    def test_get_essay_types(self, scholarship_id: str):
        """Test getting essay types for a scholarship"""
        try:
            response = requests.get(f"{self.base_url}/api/v1/scholarships/{scholarship_id}/essays")
            if response.status_code == 200:
                data = response.json()
                essay_types = data.get("essay_types", [])
                print(f"✅ Found {len(essay_types)} essay types for {scholarship_id}:")
                for essay_type in essay_types:
                    print(f"   📝 {essay_type['name']} ({essay_type['id']})")
                    print(f"      Criteria: {essay_type['criteria_count']}")
                    print(f"      Max words: {essay_type.get('max_words', 'Not specified')}")
                return essay_types
            else:
                print(f"❌ Failed to get essay types: {response.status_code}")
                return []
        except Exception as e:
            print(f"❌ Error getting essay types: {e}")
            return []
    
    def test_criteria_check(self, text: str, scholarship_type: str, essay_type: str, title: str):
        """Test the criteria checking functionality"""
        try:
            payload = {
                "text": text,
                "scholarship_type": scholarship_type,
                "essay_type": essay_type
            }
            
            start_time = time.time()
            response = requests.post(f"{self.base_url}/api/v1/criteria-check",
                                   json=payload,
                                   headers={"Content-Type": "application/json"},
                                   timeout=60)
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                self.display_criteria_results(data, title, end_time - start_time)
                return data
            else:
                print(f"❌ Criteria check failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error in criteria check: {e}")
            return None
    
    def display_criteria_results(self, results: Dict[str, Any], title: str, response_time: float):
        """Display criteria check results in a formatted way"""
        print(f"\n📊 Results for: {title}")
        print(f"Overall Score: {results['overall_score']}/100")
        print(f"Embedding Available: {results.get('embedding_available', False)}")
        print(f"Response Time: {response_time:.2f} seconds")
        
        # Analysis details
        if results.get('analysis_details'):
            details = results['analysis_details']
            print(f"Word Count: {details.get('word_count', 'N/A')}")
            if details.get('max_words'):
                print(f"Max Words: {details['max_words']}")
            print(f"Scholarship: {details.get('scholarship', 'N/A')}")
            print(f"Essay Type: {details.get('essay_type', 'N/A')}")
            if details.get('embedding_similarity'):
                print(f"Embedding Similarity: {details['embedding_similarity']:.1f}/100")
        
        # Individual criteria results
        print("\nCriteria Analysis:")
        for match in results.get('matches', []):
            score = match['score']
            level = match['match_level']
            criteria = match['criteria']
            explanation = match['explanation']
            
            # Color coding based on score
            if score >= 80:
                status = "🟢 Excellent"
            elif score >= 60:
                status = "🟡 Good"
            else:
                status = "🔴 Needs Improvement"
            
            print(f"\n  • {criteria}")
            print(f"    Score: {score}/100 ({level.upper()}) {status}")
            print(f"    {explanation}")
        
        # Overall assessment
        overall = results['overall_score']
        if overall >= 80:
            assessment = "🌟 Excellent essay! Ready for submission."
        elif overall >= 60:
            assessment = "👍 Good essay with room for improvement."
        else:
            assessment = "📝 Needs significant improvement before submission."
        
        print(f"\n🎯 Overall Assessment: {assessment}")
    
    def run_comprehensive_demo(self):
        """Run the complete demonstration"""
        self.print_header("SCHOLARSHIP CRITERIA EVALUATION DEMO")
        
        # Check service health
        if not self.check_service_health():
            return
        
        # Test 1: Get all scholarships
        self.print_section("1. Testing Scholarship Retrieval")
        scholarships = self.test_get_scholarships()
        
        if not scholarships:
            print("❌ Cannot proceed without scholarships")
            return
        
        # Test 2: Get essay types for each scholarship
        self.print_section("2. Testing Essay Type Retrieval")
        for scholarship in scholarships[:3]:  # Test first 3 scholarships
            print(f"\n📚 Essay types for {scholarship['name']}:")
            essay_types = self.test_get_essay_types(scholarship['id'])
        
        # Test 3: Criteria checking with sample essays
        self.print_section("3. Testing Criteria Evaluation")
        
        # Sample essays for different scholarships
        test_cases = [
            {
                "title": "GKS Personal Statement - High Quality",
                "scholarship_type": "gks",
                "essay_type": "personal_statement",
                "text": """
                The first time I witnessed a child's face light up when they finally grasped a mathematical concept I had been teaching, I knew that education was not just my career choice—it was my calling. This moment, in a small tutoring center in rural Vietnam, crystallized my understanding that learning transcends language barriers and cultural differences.

                My goal is to pursue a Master's in Artificial Intelligence at Seoul National University, specifically focusing on natural language processing for Southeast Asian languages. Korea's leadership in AI research, combined with companies like Samsung and LG pioneering human-computer interaction, makes it the ideal environment for my research. Upon completion, I plan to return to Thailand to establish an AI research lab that bridges Korean technological innovation with Southeast Asian linguistic diversity.

                During my undergraduate studies in Computer Science at the National University of Technology, I maintained a GPA of 3.9/4.0 while conducting research on machine learning applications. My thesis on "Deep Learning for Low-Resource Language Processing" was published in the ACL conference and earned the Best Student Paper Award. I completed advanced coursework in artificial intelligence, natural language processing, and computational linguistics.

                As president of the University AI Research Club, I organized international workshops that attracted over 200 participants from 10 countries. I founded a coding bootcamp for underprivileged youth, training over 150 students in programming fundamentals. Additionally, I volunteered as an English tutor for rural students and led our team to victory in the National AI Challenge.

                I have received the Dean's List recognition for four consecutive semesters, published three peer-reviewed papers, and developed proficiency in Python, TensorFlow, and multiple Southeast Asian languages. These experiences have prepared me for advanced research in AI and cross-cultural technology transfer.
                """
            },
            {
                "title": "Chevening Leadership Essay - Medium Quality",
                "scholarship_type": "chevening",
                "essay_type": "leadership_essay",
                "text": """
                During my time as student council president, I had to lead our university through a challenging period when budget cuts threatened to eliminate several student programs. I organized meetings with administrators, coordinated student protests, and negotiated compromises that saved most programs.

                My leadership style focuses on collaboration and finding win-win solutions. I believe in listening to all stakeholders and building consensus rather than imposing decisions. This approach helped me successfully mediate conflicts between different student groups and maintain unity during difficult times.

                I also demonstrated leadership in my internship at a consulting firm, where I led a team of five interns on a market research project. Despite initial challenges with team coordination, I implemented regular check-ins and clear task assignments that improved our efficiency and delivered results ahead of schedule.

                In the future, I plan to use my Chevening experience to become a policy advisor in my home country, where I can apply the leadership skills and international perspective gained in the UK to address local challenges and promote sustainable development.
                """
            },
            {
                "title": "DAAD Motivation Letter - Low Quality",
                "scholarship_type": "daad",
                "essay_type": "motivation_letter",
                "text": """
                I want to study in Germany because it has good universities and I like German culture. I think studying abroad will be a good experience for me and help me in my career.

                I studied engineering in my home country and got decent grades. I learned about different subjects and did some projects during my studies. I think German universities will provide better education and research opportunities.

                I plan to study mechanical engineering and maybe do some research. After graduation, I want to work for a German company or return to my country to find a good job.

                Germany seems like a nice place to live and I want to learn German language. I think this scholarship will help me achieve my goals and give me international experience.
                """
            }
        ]
        
        # Test each case
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🧪 Test Case {i}: {test_case['title']}")
            result = self.test_criteria_check(
                test_case['text'],
                test_case['scholarship_type'],
                test_case['essay_type'],
                test_case['title']
            )
        
        # Test 4: Error handling
        self.print_section("4. Testing Error Handling")
        
        # Test empty text
        print("Testing empty text...")
        response = requests.post(f"{self.base_url}/api/v1/criteria-check",
                               json={"text": "", "scholarship_type": "gks", "essay_type": "personal_statement"})
        if response.status_code == 400:
            print("✅ Empty text properly rejected")
        else:
            print(f"❌ Empty text handling failed: {response.status_code}")
        
        # Test invalid scholarship
        print("Testing invalid scholarship...")
        response = requests.post(f"{self.base_url}/api/v1/criteria-check",
                               json={"text": "test", "scholarship_type": "invalid", "essay_type": "personal_statement"})
        if response.status_code == 400:
            print("✅ Invalid scholarship properly rejected")
        else:
            print(f"❌ Invalid scholarship handling failed: {response.status_code}")
        
        # Summary
        self.print_section("DEMO SUMMARY")
        print("🎉 Scholarship Criteria Evaluation System is working!")
        print("\n📋 Features Tested:")
        print("  ✅ Scholarship retrieval")
        print("  ✅ Essay type retrieval")
        print("  ✅ Criteria-based evaluation")
        print("  ✅ Embedding similarity integration")
        print("  ✅ Error handling")
        print("  ✅ Multiple scholarship types")
        print("  ✅ Detailed feedback generation")
        
        print(f"\n🌐 API Endpoints:")
        print(f"  GET  {self.base_url}/api/v1/scholarships")
        print(f"  GET  {self.base_url}/api/v1/scholarships/<id>/essays")
        print(f"  POST {self.base_url}/api/v1/criteria-check")
        
        print("\n📚 Ready for frontend integration!")

if __name__ == "__main__":
    demo = ScholarshipCriteriaDemo()
    demo.run_comprehensive_demo()
