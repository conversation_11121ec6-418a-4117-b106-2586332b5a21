# Essay Evaluation Service - Testing and Automation Guide

This guide explains how to use the automated testing and deployment system for the Essay Evaluation Service.

## Quick Start

### Windows Users
```bash
# Run the interactive menu
test_and_run.bat

# Or use the original script
run.bat
```

### All Platforms
```bash
# Quick validation and testing
python run_tests.py --quick

# Comprehensive testing
python run_tests.py --comprehensive

# Environment validation only
python validate_env.py

# Manual testing
python test_comprehensive.py
```

## Testing Scripts Overview

### 1. `validate_env.py`
**Purpose**: Validates environment configuration and API connectivity
- ✅ Checks `.env` file exists and has required variables
- ✅ Validates OpenRouter API key format
- ✅ Tests API connectivity with a simple request
- ✅ Tests basic evaluation function

**Usage**:
```bash
python validate_env.py
```

### 2. `test_comprehensive.py`
**Purpose**: Comprehensive unit and integration tests using pytest
- ✅ Tests core evaluation logic (fast_rules, enhanced_fast_rules)
- ✅ Tests all API endpoints (/health, /api/v1/evaluate, /api/v1/criteria, /api/v1/analyze)
- ✅ Tests error handling and edge cases
- ✅ Tests environment configuration
- ✅ Integration tests with mocked LLM responses

**Usage**:
```bash
# With pytest (recommended)
python -m pytest test_comprehensive.py -v

# Direct execution
python test_comprehensive.py
```

### 3. `automate_test.py`
**Purpose**: Full automation script with server management
- ✅ Checks prerequisites and installs dependencies
- ✅ Runs unit tests
- ✅ Starts Flask server automatically
- ✅ Runs integration tests against live server
- ✅ Performance testing with different essay types
- ✅ Generates comprehensive test reports

**Usage**:
```bash
python automate_test.py
```

### 4. `run_tests.py`
**Purpose**: Master test orchestrator with multiple modes
- ✅ Environment validation
- ✅ Dependency installation
- ✅ Unit testing
- ✅ Integration testing
- ✅ Performance testing (comprehensive mode)
- ✅ Detailed reporting

**Usage**:
```bash
# Quick tests
python run_tests.py --quick

# Comprehensive tests
python run_tests.py --comprehensive

# Environment validation only
python run_tests.py --env-only

# Skip dependency installation
python run_tests.py --no-install
```

### 5. `test_simple.py`
**Purpose**: Simple integration tests for manual verification
- ✅ Basic health check
- ✅ Simple evaluation test
- ✅ Manual result inspection

**Usage**:
```bash
# Make sure service is running first
python app.py

# Then in another terminal
python test_simple.py
```

## Test Categories

### Unit Tests
- **Fast Rules Testing**: Word count, structure, keyword analysis
- **Enhanced Rules Testing**: Detailed analysis with feedback
- **API Endpoint Testing**: All Flask routes and error handling
- **Environment Testing**: Configuration validation

### Integration Tests
- **Live API Testing**: Tests against running Flask server
- **End-to-End Workflow**: Complete evaluation pipeline
- **Error Handling**: Invalid inputs and edge cases

### Performance Tests
- **Response Time Testing**: Different essay lengths
- **Load Testing**: Multiple concurrent requests
- **Memory Usage**: Resource consumption analysis

## Environment Requirements

### Required Files
- `.env` - Environment configuration
- `app.py` - Flask application
- `evaluation.py` - Core evaluation logic
- `requirements.txt` - Python dependencies

### Environment Variables (.env)
```
OPENROUTER_API_KEY=sk-or-v1-your-api-key-here
OPENROUTER_BASE=https://openrouter.ai/api/v1
MODEL_ID=anthropic/claude-3.5-sonnet
```

### Python Dependencies
```
Flask==2.3.3
Flask-CORS==4.0.0
requests==2.31.0
python-dotenv==1.0.0
gunicorn==21.2.0
pytest==7.4.3
pytest-flask==1.3.0
pytest-cov==4.1.0
```

## Test Reports

### Generated Files
- `test_results.json` - Detailed test execution report
- `test_report.json` - Comprehensive system analysis
- `__pycache__/` - Python bytecode cache

### Report Contents
- Execution timestamps
- Test phase results
- Performance metrics
- Error details
- System information

## Troubleshooting

### Common Issues

1. **Missing .env file**
   ```
   ❌ .env file not found
   ```
   **Solution**: Create `.env` file with required variables

2. **Invalid API key**
   ```
   ❌ API key appears to be placeholder or invalid format
   ```
   **Solution**: Get valid API key from https://openrouter.ai/

3. **Import errors**
   ```
   ❌ Import error: No module named 'flask'
   ```
   **Solution**: Install dependencies with `pip install -r requirements.txt`

4. **Server startup issues**
   ```
   ❌ Server failed to start within 30 seconds
   ```
   **Solution**: Check port 5000 is available, verify Python path

5. **API connectivity issues**
   ```
   ❌ API request failed: 401
   ```
   **Solution**: Verify API key is correct and has sufficient credits

### Debug Mode
Enable debug output by setting environment variable:
```bash
export FLASK_DEBUG=true  # Linux/Mac
set FLASK_DEBUG=true     # Windows
```

## Best Practices

### Before Deployment
1. Run `python validate_env.py` to check configuration
2. Run `python run_tests.py --comprehensive` for full validation
3. Verify all tests pass before deploying

### During Development
1. Use `python run_tests.py --quick` for rapid feedback
2. Run specific test files for focused testing
3. Check test reports for performance insights

### Production Readiness
1. All tests must pass with 100% success rate
2. API connectivity must be verified
3. Performance tests should show acceptable response times
4. Error handling must be thoroughly tested

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review test output for specific error messages
3. Verify environment configuration with `validate_env.py`
4. Check API key validity and credits at OpenRouter dashboard
